import * as React from "react";
import {create<PERSON>ontext, Dispatch, FC, Reducer, useContext, useEffect, useMemo, useReducer} from "react";
import {
    cloneSmartStepState,
    createSmartStepInit,
    emitSmartStepCommand,
    emitSmartStepErrors,
    SmartStepData,
    SmartStepInit,
    smartStepInitLookup,
    SmartStepState
} from "./smartsteps/SmartStepRoot";
import {CommandQueue, CommandSource} from "ecco-commands";
import {
    useCommandSourceRegistration,
    useCommandSourceStateHolder,
    useServicesContext
} from "ecco-components";
import {
    Action,
    ConfigResolver,
    HactCheckEvent,
    Question,
    QuestionAnswerSnapshotDto,
    QuestionAnswerTransientEvent,
    ServiceType,
    SessionData,
    SupportAction
} from "ecco-dto";
import {EvidenceDef, HierarchyPosition} from "./domain";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {
    cloneQuestionAnswerState,
    createQuestionAnswerInit,
    emitQuestionAnswerCommand,
    emitQuestionAnswerErrors,
    QuestionAnswerData,
    QuestionAnswerInit,
    questionAnswerInitLookup,
    QuestionAnswerState
} from "./questionanswers/QuestionAnswerRoot";
import {
    cloneCommentEntryState,
    CommentEntryData,
    CommentEntryInit,
    CommentEntryState,
    emitCommentEntryCommand,
    emitCommentEntryErrors
} from "./CommentEntryRoot";
import {EccoDate, EccoDateTime, SelectListOption, UploadResult} from "@eccosolutions/ecco-common";

/***********
 * STRATEGY
 *
 * Initially, we had each component (eg SmartStep.tsx) have a reducer to avoid growing prop-madness.
 * However, context's are obviously shared, and multiple components tipped the complexity threshold over.
 * Using prop-based Its also better for each component to use props for unit-like testing, so its reverted.
 *
 * EvidencePage is more appropriate for a reducer as its the top-level (eg see care*.tsx). Things could
 * be achieved without a reducer - see MultiEntryEvidenceForm, and see useMemo<{state: State which
 * gives us stateSetter's in FC's by using the memo to keep away the state lifecycle. However, it
 * feels like the logic here will grow, and adding reducer complexity now is beneficial.
 * The downside is often testability, but we've been careful to keep injection points to control the data.
 *
 * In the end, this gives us testable underlying components, and a testable EvidencePage via injection points.
 */


// EXAMPLE FLOW:
// TasksControl (srId, taskName)
//   -> EvidencePageLoaderForCommandForm (srId, taskName) ** LOADS initData **, child is EvidencePagePlanLayout
//     -> EvidencePageSetupForCommandForm (with initData), child pass through
//       -> EvidencePageRootForCommandForm (with initData), child is...
//         = <EvidencePageRoot><EvidencePageCommandForm>{child}
//         -> EvidencePageRoot (with initData) ** CREATES STATE in memo createEvidencePageInit **
//           -> EvidencePageContextProvider memos data
//             -> EvidencePageCommandForm (registers command forms)
//               -> child is EvidencePagePlanLayout ** FILTERS STATE for page **


/******************
 * DATA STRUCTURES
 */


/**
 * Incoming/loaded data
 */
export interface EvidencePageData {
    sessionData: SessionData;
    serviceId: number; // useful for Hact control
    taskName: string; // could be within evidenceDef
    evidenceDef: EvidenceDef; // refactor out
    configResolver: ConfigResolver; // refactor out
    workUuid: Uuid;
    workUuid2: Uuid; // for our wip mixed smartstep+questionnaire page, we create entirely separate histories and so need a second workUuid
    serviceRecipientId: number;
    readOnly: boolean;
    comment: string | undefined;
    commentTypeId: number | undefined;
    commentTypes: SelectListOption[];
    clientStatusId: number | undefined;
    meetingStatusId: number | undefined;
    workDate: EccoDate | undefined;
    minsSpent: number | undefined;
    supportActions: SupportAction[];
    questionAnswers: QuestionAnswerSnapshotDto[];
}

/**
 * Initialised data
 */
export interface EvidencePageInit {
    initData: EvidencePageData;
    initCommentEntry: CommentEntryInit;
    initSmartSteps: SmartStepInit[];
    initQuestionAnswers: QuestionAnswerInit[];
    initState: EvidencePageState;
}

export interface EvidencePageFilter {
    display: boolean;
    achievedIn: boolean;
    achievedInDefault: boolean;
}

/**
 * User state current data
 */
export interface EvidencePageState {
    pageEditing: boolean;
    commentEntry: CommentEntryState;
    smartSteps: SmartStepState[];
    filter: EvidencePageFilter;
    questionAnswers: QuestionAnswerState[];
    // we either have a reducer for EvidencePageInit, merge the two interfaces, or have this
    // at least this approach is clear its newly provisioned and could help with offline? init being snapshot vs provisioned
    smartStepsProvisioned: SmartStepInit[];
    // we either have a reducer for EvidencePageInit, merge the two interfaces, or have this
    // at least this approach is clear its newly provisioned and could help with offline? init being snapshot vs provisioned
    questionAnswersProvisioned: QuestionAnswerInit[];
    attachments: UploadResult[];
}

// try a comparison without using the whole emitCommand
// although we could re-use draftSave / state decorator approaches
// although this approach does rely on ordering of properties (which is the case now)
// NB EvidencePageState would be good to compare, but initHolder.initState IS UPDATED WITH STATE!
// so we need to find somewhere where the spread isn't used, or is used but only on first-level props (shalow)
//      https://joel-colombo.medium.com/understanding-typescript-deep-object-spreading-f0c1ea8ad25f
export function stateDifferent(oldState: SmartStepState[], newState: SmartStepState[]): boolean {
    const jsonOld = JSON.stringify(oldState);
    const jsonNew = JSON.stringify(newState);
    // we can only be sure of this comparison if we assume simple objects
    // see https://stackoverflow.com/questions/15376185/is-it-fine-to-use-json-stringify-for-deep-comparisons-and-cloning
    return jsonOld !== jsonNew;
}

/**
 * Actions to modify state
 */
export type EvidencePageActionType =
    // { type: 'hideVisit' } |
    // { type: 'commandFailed', reason: any } |
    | {type: "pageEdit"}
    | {type: "filter"; filter: EvidencePageFilter}
    | {type: "removeSmartStep"; controlUuid: Uuid}
    | {type: "updateSmartStep"; controlUuid: Uuid; data: Partial<SmartStepState>}
    | {type: "updateQuestionAnswer"; questionDefId: number; data: Partial<QuestionAnswerState>}
    | {
          type: "updateCommentEntry";
          data: Partial<{
              comment?: string | undefined;
              workDate?: EccoDate | EccoDateTime | undefined;
              minsSpent?: number | undefined;
              commentTypeId?: number | undefined;
              clientStatusId?: number | undefined;
              meetingStatusId?: number | undefined;
          }>;
      }
    | {
          type: "addSmartStep";
          initData: EvidencePageData;
          transientOutcomeDefId?: number;
          transientActionDefId?: number;
      }
    | {
          type: "addSubStep";
          initData: EvidencePageData;
          parentControlUuid: Uuid;
      }
    | {type: "addQuestionAnswer"; initData: EvidencePageData; questionDefId: number}
    | {type: "updateAttachments"; attachments: UploadResult[]}
    | {type: "removeAttachments"; fileIds: number[]};


/********
 * LOGIC
 */
export function allowMixedQuestionsAndSmartSteps(sessionData: SessionData, taskName: string): boolean {
    return !!sessionData.getTaskDefinitionByName(taskName)?.metadata?.questionsToActions;
}
// the idea that a support evidence page can show just questions
// its where we have no smart steps configured at all - well, they are all '0'
// this is currently a WIP to indicate that we just want questions but on a smart step evidence page
// (which triggers other things we need on the page)
export function allowMixedPageTotalSwitch(initData: EvidencePageData) {
    const actionDefIds = initData.sessionData.getActionDefIdsInTask(initData.taskName);
    return actionDefIds.length > 0 && !actionDefIds.some(id => id > 0)
}

/*export function hasMixedQuestionsTaskName(sessionData: SessionData, taskName: string): boolean {
    return !!sessionData.getTaskDefinitionByName(taskName)?.metadata?.questionsTaskName;
}*/

/**
 * Override EvidenceGroup as 'needs' for recording smart step data.
 * Ideally we keep the taskName, but a mixed evidence data on a single EVIDENCE_ type would cause issues
 * So for now just use assessment+reduction (so can star).
 */
export function createEvidenceDefForQuestionnaireWithSmartSteps(sessionData: SessionData, serviceType: ServiceType) {
    return EvidenceDef.fromTaskName(sessionData, serviceType, "needsAssessmentReduction");
}
/**
 * Override EvidenceGroup as the questionnaire group for recording questionnaire data.
 * Ideally we keep the taskName, but a mixed evidence data on a single EVIDENCE_ type would cause issues
 * So for now just use what's configured.
 */
export function createEvidenceDefForSmartStepsWithQuestionnaire(sessionData: SessionData, taskName: string, serviceType: ServiceType) {
    const taskNameForQns =
        sessionData.getTaskDefinitionByName(taskName)?.metadata?.questionsTaskName;
    return taskNameForQns
        ? EvidenceDef.fromTaskName(sessionData, serviceType, taskNameForQns)
        : null;
}

export const evidencePageReducer: EvidencePageReducer = (
        state: EvidencePageState,
        action: EvidencePageActionType
): EvidencePageState => {
    // <-- Error here if you add a type bug don't add to switch(action.type) cases

    switch (action.type) {
        case "pageEdit": {
            return {
                ...state,
                pageEditing: true
            };
        }
        case "filter": {
            return {
                ...state,
                filter: action.filter
            };
        }
        case "removeSmartStep": {
            const newItems = removeSingleEntry(action.controlUuid, state.smartSteps);
            return {
                ...state,
                smartSteps: newItems
            };
        }

        case "updateSmartStep": {
            // cmd can just have isRelevant for link/associated action
            const newItems = updateSingleEntry(action.controlUuid, state.smartSteps, action.data);
            return {
                ...state,
                smartSteps: newItems
            };
        }

        case "updateQuestionAnswer": {
            const newItems = updateSingleEntryQuestion(
                action.questionDefId,
                state.questionAnswers,
                action.data
            );

            QuestionAnswerTransientEvent.bus.fire(
                new QuestionAnswerTransientEvent(action.questionDefId, action.data.answer || null)
            );

            return {
                ...state,
                questionAnswers: newItems
            };
        }

        case "updateCommentEntry": {
            const newState: CommentEntryState = {
                ...state.commentEntry
            };
            if (action.data.comment) {
                newState.comment = action.data.comment;
            }
            if (action.data.commentTypeId) {
                newState.commentTypeId = action.data.commentTypeId;
            }
            if (action.data.clientStatusId) {
                newState.clientStatusId = action.data.clientStatusId;
            }
            if (action.data.meetingStatusId) {
                newState.meetingStatusId = action.data.meetingStatusId;
            }
            if (action.data.workDate) {
                newState.workDate = action.data.workDate;
            }
            if (action.data.minsSpent) {
                newState.minsSpent = action.data.minsSpent;
            }
            return {
                ...state,
                commentEntry: newState
            };
        }

        case "addSmartStep": {
            const allowMixed = allowMixedQuestionsAndSmartSteps(
                action.initData.sessionData,
                action.initData.taskName
            );

            const newSmartStepData = createSmartStepData(
                action.initData,
                action.transientActionDefId,
                action.transientOutcomeDefId,
                undefined,
                allowMixed
            );
            const newSmartStepInit = createSmartStepInit(newSmartStepData);

            return {
                ...state,
                smartSteps: state.smartSteps.concat(newSmartStepInit.initState),
                smartStepsProvisioned: state.smartStepsProvisioned.concat(newSmartStepInit)
            };
        }

        case "addSubStep": {
            const allowMixed = allowMixedQuestionsAndSmartSteps(
                action.initData.sessionData,
                action.initData.taskName
            );

            const newSmartStepData = createSmartStepData(
                action.initData,
                undefined,
                undefined,
                undefined,
                allowMixed
            );

            const parent = state.smartSteps.find(s =>
                s.controlUuid.equals(action.parentControlUuid)
            )!;

            // create, then apply the known data
            const newSmartStepInit = createSmartStepInit(newSmartStepData);

            // NB the controlUuid/parent becomes the actionInstanceUuid/parent - see emitSmartStepCommand
            newSmartStepInit.initState.actionDefId = parent.actionDefId;
            newSmartStepInit.initState.parentControlUuid = action.parentControlUuid;
            const hierarchy = 1;
            newSmartStepInit.initState.hierarchy = hierarchy;
            const nextPosition = state.smartSteps.filter(s =>
                s.parentControlUuid?.equals(action.parentControlUuid)
            ).length;
            newSmartStepInit.initState.position = HierarchyPosition.builder(
                hierarchy,
                nextPosition
            );

            return {
                ...state,
                smartSteps: state.smartSteps.concat(newSmartStepInit.initState),
                smartStepsProvisioned: state.smartStepsProvisioned.concat(newSmartStepInit)
            };
        }

        case "addQuestionAnswer": {
            const newQuestionAnswerData = createQuestionAnswerData(
                action.initData,
                action.questionDefId,
                undefined,
                true
            );
            const newQuestionAnswerInit = createQuestionAnswerInit(newQuestionAnswerData);

            return {
                ...state,
                questionAnswers: state.questionAnswers.concat(newQuestionAnswerInit.initState),
                questionAnswersProvisioned:
                    state.questionAnswersProvisioned.concat(newQuestionAnswerInit)
            };
        }

        case "updateAttachments": {
            return {
                ...state,
                attachments: [...state.attachments, ...action.attachments]
            };
        }

        case "removeAttachments": {
            return {
                ...state,
                attachments: state.attachments.filter(
                    attachment => attachment.fileId && !action.fileIds.includes(attachment.fileId)
                )
            };
        }
    }
};

// based on MultiEntryEvidenceForm, but not using immutability-helper
function updateSingleEntry<T extends {controlUuid: Uuid | null}>(
    controlUuid: Uuid,
    entries: T[],
    entry: Partial<T>
) {
    entries.forEach((e, i) => {
        if (e.controlUuid!.equals(controlUuid)) {
            entries[i] = {...e, ...entry};
        }
    });
    return entries;
}

function removeSingleEntry<T extends {controlUuid: Uuid | null}>(controlUuid: Uuid, entries: T[]) {
    let removeIndex: number | null = null;
    entries.forEach((e, i) => {
        if (e.controlUuid!.equals(controlUuid)) {
            removeIndex = i;
        }
    });
    if (removeIndex != null) {
        entries.splice(removeIndex, 1);
    }
    return entries;
}

function updateSingleEntryQuestion<T extends {questionDefId: number | null}>(
    questionDefId: number,
    entries: T[],
    entry: Partial<T>
) {
    entries.forEach((e, i) => {
        if (e.questionDefId == questionDefId) {
            entries[i] = {...e, ...entry};
        }
    });
    return entries;
}

const emitCommands = (
    cmdQ: CommandQueue,
    initHolder: {init: EvidencePageInit},
    stateHolder: {state: EvidencePageState}
) => {
    //     const relatedActionIds = stateHolder.state.smartSteps
    //         .filter(s => s.related)
    emitCommentEntryCommand(initHolder.init.initCommentEntry, stateHolder.state.commentEntry, cmdQ);
    stateHolder.state.smartSteps.map(s => {
        const init = smartStepInitLookup(
            initHolder.init.initSmartSteps,
            stateHolder.state.smartStepsProvisioned,
            s.controlUuid
        );
        emitSmartStepCommand(init, s, cmdQ);
    });
    stateHolder.state.questionAnswers.map(s => {
        const init = questionAnswerInitLookup(
            initHolder.init,
            stateHolder.state.questionAnswersProvisioned,
            s.questionDefId
        );
        emitQuestionAnswerCommand(init, s, cmdQ);
    });
};
const emitErrors = (initHolder: {init: EvidencePageInit}, stateHolder: {state: EvidencePageState}): string[] => {
    let errors: string[] = [];
    errors = errors.concat(
        emitCommentEntryErrors(initHolder.init.initCommentEntry, stateHolder.state.commentEntry)
    );
    stateHolder.state.smartSteps.map(s => {
        const init = smartStepInitLookup(
            initHolder.init.initSmartSteps,
            stateHolder.state.smartStepsProvisioned,
            s.controlUuid
        );
        errors = errors.concat(emitSmartStepErrors(init, s));
    });
    stateHolder.state.questionAnswers.map(s => {
        const init = questionAnswerInitLookup(
            initHolder.init,
            stateHolder.state.questionAnswersProvisioned,
            s.questionDefId
        );
        errors = errors.concat(emitQuestionAnswerErrors(init, s));
    });
    return [];
}

function createEvidencePageInit(
    sessionData: SessionData,
    initData: EvidencePageData
): EvidencePageInit {
    const type = EvidenceDef.taskEvidenceType(sessionData, initData.taskName)!;
    // questionnaires and smart steps together (either on a questionnaire page, or a smart step page)
    const allowMixed = allowMixedQuestionsAndSmartSteps(sessionData, initData.taskName);

    let smartStepInits: SmartStepInit[] = [];
    let smartStepStates: SmartStepState[] = [];
    if (EvidenceDef.isSupport(type)) {
        smartStepInits = createSmartStepInits(initData, false);
        smartStepStates = createSmartStepStates(smartStepInits);
    }

    let questionAnswerInits: QuestionAnswerInit[] = [];
    let questionAnswerStates: QuestionAnswerState[] = [];
    if (EvidenceDef.isQuestionnaire(type)) {
        questionAnswerInits = createQuestionAnswerInits(initData, false);
        questionAnswerStates = createQuestionAnswerStates(questionAnswerInits);
    }

    if (allowMixed) {
        if (EvidenceDef.isQuestionnaire(type)) {
            const allowedActionDefIds = sessionData.getActionDefIdsInTask(initData.taskName);
            const supportActionsDefIdsLoaded = initData.supportActions.map(s => s.actionId);
            const uniqueActionDefIds = [
                ...new Set(
                    supportActionsDefIdsLoaded.filter(aId => allowedActionDefIds.indexOf(aId) > -1)
                )
            ];

            const smartStepInitsAll = createSmartStepInits(initData, true, uniqueActionDefIds);
            uniqueActionDefIds.forEach(actionDefId => {
                const inits = smartStepInitsAll.filter(s => s.initData.actionDefId == actionDefId);
                smartStepStates.push(
                    ...inits.map(i => i.initState).map(s => cloneSmartStepState(s))
                );
                smartStepInits.push(...inits);
            });
        }

        if (EvidenceDef.isSupport(type)) {
            const totalSwitch = allowMixedPageTotalSwitch(initData);

            if (totalSwitch) {
                const allowedQuestionDefIds = sessionData.getQuestionDefIdsInTask(
                    initData.taskName
                );
                questionAnswerInits = createQuestionAnswerInits(
                    initData,
                    false,
                    allowedQuestionDefIds
                );
                questionAnswerStates = createQuestionAnswerStates(questionAnswerInits);
            } else {
                const allowedQuestionDefIds = sessionData.getQuestionDefIdsInTask(
                    initData.taskName
                );
                const questionAnswersDefIdsLoaded = initData.questionAnswers.map(s => s.questionId);
                const uniqueQuestionDefIds = [
                    ...new Set(
                        questionAnswersDefIdsLoaded.filter(
                            qId => allowedQuestionDefIds.indexOf(qId) > -1
                        )
                    )
                ];

                const questionAnswerInitsAll = createQuestionAnswerInits(
                    initData,
                    true,
                    uniqueQuestionDefIds
                );
                uniqueQuestionDefIds.forEach(questionDefId => {
                    const inits = questionAnswerInitsAll.filter(
                        q => q.initData.questionDefId == questionDefId
                    );
                    questionAnswerStates.push(
                        ...inits.map(i => i.initState).map(s => cloneQuestionAnswerState(s))
                    );
                    questionAnswerInits.push(...inits);
                });
            }
        }
    }

    const commentEntryInitData: CommentEntryData = createCommentEntryData(initData);
    const commentEntryInit: CommentEntryInit = createCommentEntryInit(
        initData.sessionData,
        commentEntryInitData
    );
    const commentEntryInitStateCloned = cloneCommentEntryState(commentEntryInit.initState);

    // exclude actions if we are an assessment page
    // NB see getEvidenceActAs - avoid the hard coded EvidencePageType - initData.evidenceDef.getEvidencePageType() == EvidencePageType.assessment;
    const alwaysHideAchieved = initData.configResolver
        .getServiceType()
        .taskDefinitionSettingHasFlag(initData.evidenceDef.getTaskName(), "actAs", "assessment");

    const evidencePageState: EvidencePageState = {
        pageEditing: !initData.readOnly,
        filter: {
            display: true,
            achievedIn: !alwaysHideAchieved,
            achievedInDefault: !alwaysHideAchieved
        },
        commentEntry: commentEntryInitStateCloned,
        smartSteps: smartStepStates,
        questionAnswers: questionAnswerStates,
        smartStepsProvisioned: [],
        questionAnswersProvisioned: [],
        attachments: initData.readOnly
            ? []
            : [
                  // Mock attachments for testing the badge functionality
                  {
                      fileId: 1,
                      filename: "evidence-document.pdf",
                      size: 1024000,
                      type: "application/pdf",
                      links: []
                  },
                  {
                      fileId: 2,
                      filename: "photo-evidence.jpg",
                      size: 512000,
                      type: "image/jpeg",
                      links: []
                  }
              ]
    };

    return {
        initData: initData,
        initCommentEntry: commentEntryInit,
        initSmartSteps: smartStepInits,
        initQuestionAnswers: questionAnswerInits,
        initState: evidencePageState
    };
}

function createSmartStepData(
    initData: EvidencePageData,
    transientActionDefId: number | undefined,
    transientOutcomeDefId: number | undefined,
    supportAction: SupportAction | undefined,
    fudgeEvidencePage: boolean
): SmartStepData {
    // if we are actually a questionnaire page with smart steps, then
    // override EvidenceGroup as 'needs' for smart steps, but keep everything else
    // see SmartStepRoot emit.. for what is needed
    if (fudgeEvidencePage) {
        // NB override hardcoded evidenceDef - needsAssessmentReduction
        const evidenceDefForSupport = createEvidenceDefForQuestionnaireWithSmartSteps(
            initData.sessionData,
            initData.configResolver.getServiceType()
        );
        return {
            snapshot: supportAction || null,
            actionDefId: transientActionDefId,
            transientOutcomeDefId: transientOutcomeDefId,
            sessionData: initData.sessionData,
            evidenceDef: evidenceDefForSupport,
            configResolver: initData.configResolver,
            getWorkUuid: () => initData.workUuid2,
            serviceRecipientId: initData.serviceRecipientId,
            readOnly: initData.readOnly
        };
    }

    return {
        snapshot: supportAction || null,
        actionDefId: transientActionDefId,
        transientOutcomeDefId: transientOutcomeDefId,
        sessionData: initData.sessionData,
        evidenceDef: initData.evidenceDef,
        configResolver: initData.configResolver,
        getWorkUuid: () => initData.workUuid,
        serviceRecipientId: initData.serviceRecipientId,
        readOnly: initData.readOnly
    };
}

function createSmartStepInits(
    initData: EvidencePageData,
    fudgeEvidencePage: boolean,
    limitToActionDefIds?: number[] | undefined
): SmartStepInit[] {
    // get all actions, sorted by name of outcomes -> actiongroups -> actions
    const outcomes = initData.configResolver.getOutcomesFilteredForTask(initData.taskName);
    var actionsAvailable: Action[];
    // if we are a mixed page, where we only show certain actions, then we have configured actions directly and not in normal 'outcomes'
    // TODO ? filter out by isAnyActionByIdDisabled
    if (limitToActionDefIds) {
        actionsAvailable = limitToActionDefIds.map(id =>
            initData.sessionData.getSupportActionById(id)
        );
    } else {
        actionsAvailable = outcomes
            .map(o => o.getActionGroups())
            .reduce((r, x) => r.concat(x), []) // flatMap
            .map(ag => ag.getActions())
            .reduce((r, x) => r.concat(x), []); // flatMap
    }

    let actionsAllowed = actionsAvailable.filter(a => {
        // ensure we're allowing the action based on the right criteria of support
        return initData.supportActions.filter(s => s.actionId == a.getId());
    });

    // legacy evidence pages render SupportAction data according to supportActionsMatchesForActionDefId
    // TODO ? this only ever shows saved actions?
    return actionsAllowed
        .map(a => {
            // filter to the action
            let supportActions = initData.supportActions.filter(data => data.actionId == a.getId());

            return supportActions.map(ss => {
                const smartStepData: SmartStepData = createSmartStepData(
                    initData,
                    a.getId(),
                    undefined,
                    ss,
                    fudgeEvidencePage
                );
                return createSmartStepInit(smartStepData);
            });
        })
        .reduce((r, x) => r.concat(x), []); // flatMap;
}


function createSmartStepStates(smartStepInits: SmartStepInit[]): SmartStepState[] {
    return smartStepInits.map(i => i.initState).map(s => cloneSmartStepState(s));
}

function createQuestionAnswerData(
    initData: EvidencePageData,
    questionDefId: number,
    questionAnswer: QuestionAnswerSnapshotDto | undefined,
    fudgeEvidencePage: boolean
): QuestionAnswerData {
    // if we are actually a support page with questions, then
    // override EvidenceGroup as configured, but keep everything else
    // see QuestionAnswerRoot emit.. for what is needed
    if (fudgeEvidencePage) {
        const evidenceDefForQuestions = createEvidenceDefForSmartStepsWithQuestionnaire(
            initData.sessionData,
            initData.taskName,
            initData.configResolver.getServiceType()
        );
        return {
            snapshot: questionAnswer || null,
            questionDefId: questionDefId,
            sessionData: initData.sessionData,
            getWorkUuid: () => initData.workUuid2,
            evidenceDef: evidenceDefForQuestions!,
            configResolver: initData.configResolver,
            serviceRecipientId: initData.serviceRecipientId
        };
    }

    return {
        snapshot: questionAnswer || null,
        questionDefId: questionDefId,
        sessionData: initData.sessionData,
        getWorkUuid: () => initData.workUuid,
        evidenceDef: initData.evidenceDef,
        configResolver: initData.configResolver,
        serviceRecipientId: initData.serviceRecipientId
    };
}

function createQuestionAnswerInits(
    initData: EvidencePageData,
    fudgeEvidencePage: boolean,
    limitToQuestionDefIds?: number[] | undefined
): QuestionAnswerInit[] {
    let questions: Question[];
    const groupFromQuestion = (qId: number) =>
        initData.sessionData.getQuestionGroupByQuestionId(qId)!;
    // if we are a mixed page, where we only show certain actions, then we have configured actions directly and not in normal 'outcomes'
    if (limitToQuestionDefIds) {
        questions = limitToQuestionDefIds.map(id => initData.sessionData.getQuestionById(id));
    } else {
        // get all questiongroups, already sorted
        const questionGroups = initData.configResolver.getQuestionGroupsFilteredForTask(
            initData.taskName
        );
        questions = questionGroups.map(qg => qg.questions).reduce((r, x) => r.concat(x), []); // flatMap
    }
    return questions
        .filter(q => !q.disabled)
        .map(q => {
            const snapshotAnswers = initData.questionAnswers.filter(
                data => data.questionId == q.id
            );
            if (snapshotAnswers.length > 1) {
                throw new Error("multiple data not allowed");
            }
            const questionAnswerData: QuestionAnswerData = createQuestionAnswerData(
                initData,
                q.id,
                snapshotAnswers.pop(),
                fudgeEvidencePage
            );
            return createQuestionAnswerInit(questionAnswerData);
        });
}

function createQuestionAnswerStates(
    questionAnswerInits: QuestionAnswerInit[]
): QuestionAnswerState[] {
    return questionAnswerInits.map(i => i.initState).map(s => cloneQuestionAnswerState(s));
}

function createCommentEntryData(initData: EvidencePageData): CommentEntryData {
    return {
        sessionData: initData.sessionData,
        comment: initData.comment || null,
        workDate: initData.workDate || null,
        minsSpent: initData.minsSpent || null,
        commentTypeId: initData.commentTypeId || null,
        commentTypes: initData.commentTypes,
        clientStatusId: initData.clientStatusId || null,
        meetingStatusId: initData.meetingStatusId || null,
        evidenceDef: initData.evidenceDef,
        configResolver: initData.configResolver,
        getWorkUuid: () => initData.workUuid,
        serviceRecipientId: initData.serviceRecipientId
    };
}
function createCommentEntryInit(
    sessionData: SessionData,
    initData: CommentEntryData
): CommentEntryInit {
    const state: CommentEntryState = {
        comment: initData.comment || undefined,
        workDate: initData.workDate || undefined,
        minsSpent: initData.minsSpent || undefined,
        commentTypeId: initData.commentTypeId || undefined,
        clientStatusId: initData.clientStatusId || undefined,
        meetingStatusId: initData.meetingStatusId || undefined
    };
    return {
        initData: initData,
        initState: state
    };
}


/*********
 * WIRING
 */

/**
 * Shared/context data across components
 */
interface EvidencePageContextProps {
    init: EvidencePageInit
    state: EvidencePageState
    dispatch: Dispatch<EvidencePageActionType>
    // this would be nice, but doesn't work as a wip, even with useMemo
    //commandSourceHolder: {init: EvidencePageInit, state: EvidencePageState}
    initHolder: {init: EvidencePageInit}
    stateHolder: {state: EvidencePageState}
}

/**
 * Shared/context data across components - context
 */
const EvidencePageContext = createContext<EvidencePageContextProps | undefined>(undefined);
export const useEvidencePageContext = () => useContext(EvidencePageContext)!; // It's a dev error if we haven't initialised before use

/**
 * Shared/context data across components - provider
 */
interface EvidencePageContextProviderProps {
    init: EvidencePageInit
    reducer: Reducer<EvidencePageState, EvidencePageActionType>
}

const EvidencePageContextProvider: FC<EvidencePageContextProviderProps> = ({
    reducer,
    init,
    children
}) => {
    // const initStateFromDefault: EvidencePageState = {
    //     ...({} as EvidencePageState), // empty/default to override with partial initState
    //     ...init.initState
    // }

    // MEMOs for CommandSource
    // the memos are references that won't change, so the same reference can be used for CommandSource - which is good,
    // as its a closure, but the values inside will refer to updated values
    const {stateHolderMemo, stateHolder, dispatchHolder} = useCommandSourceStateHolder(
        init.initState
    );
    const initHolderMemo = useMemo<{init: EvidencePageInit}>(
        () => ({
            init: init
        }),
        []
    );

    // the normal reducer
    const [state, dispatch] = useReducer<EvidencePageReducer>(reducer, init.initState);

    // LINK the memos to the normal reducer
    // when the state changes (as per a normal reducer action) this effect updates the holder with the same state
    // which keeps the memo in sync for when we use CommandSource
    useEffect(() => {
        dispatchHolder(state);
    }, [state]);

    // not much difference in useMemo here probably because it depends on state changing, which is every change
    const value: EvidencePageContextProps = {
        state: stateHolder,
        dispatch,
        init,
        stateHolder: stateHolderMemo,
        initHolder: initHolderMemo
    };

    return <EvidencePageContext.Provider value={value}>{children}</EvidencePageContext.Provider>;
};

/**
 * Shared/context data across components - reducer logic
 */
export type EvidencePageReducer = React.Reducer<EvidencePageState, EvidencePageActionType>;


/***************
 * PUBLIC FACING
 */

/**
 * External entry point for the wiring
 * Fully functional EvidencePage assuming no CommandForm
 * NB There is currently no mechanism to save with this component
 */
export const EvidencePageRoot: FC<{
    initData: EvidencePageData;
    actions?: EvidencePageReducer | undefined;
}> = props => {
    const {sessionData} = useServicesContext();
    const initMemo = useMemo<EvidencePageInit>(
        () => createEvidencePageInit(sessionData, props.initData),
        []
    );

    return (
        <EvidencePageContextProvider reducer={props.actions || evidencePageReducer} init={initMemo}>
            {props.children}
        </EvidencePageContextProvider>
    );
};
EvidencePageRoot.displayName = "EvidencePageRoot";


/**
 * External entry point for the wiring
 * Fully functional EvidencePage assuming a CommandForm
 */
export const EvidencePageRootForCommandForm: FC<{
    initData: EvidencePageData;
    actions?: EvidencePageReducer | undefined;
}> = props => {
    return (
        <EvidencePageRoot initData={props.initData} actions={props.actions}>
            <EvidencePageCommandForm>{props.children}</EvidencePageCommandForm>
        </EvidencePageRoot>
    );
};

const EvidencePageCommandForm: FC = props => {
    const {stateHolder, initHolder} = useEvidencePageContext();

    const cmdSrc: CommandSource = {
        emitChangesTo: function (cmdQ: CommandQueue): void {
            emitCommands(cmdQ, initHolder, stateHolder);
        },
        getErrors(): string[] {
            return emitErrors(initHolder, stateHolder);
        }
    };

    const checkHactOnFinish = () => {
        HactCheckEvent.bus.fire();
    };

    useCommandSourceRegistration(cmdSrc, checkHactOnFinish);

    //useFormDraftSave();

    return <>{props.children}</>;
};

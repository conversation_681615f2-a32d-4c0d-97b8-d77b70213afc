import * as React from "react";
import {CustomFormFields, SchemaForm, update} from "ecco-components";
import {FormDefinition, FormEvidence} from "ecco-dto";

export interface SchemaProps<FORM_DATA extends CustomFormFields> {
    readOnly: boolean;
    formEvidence: FormEvidence<FORM_DATA> | null; // TODO: rename to previousFormEvidence
    /** Renders nothing if !formDefinition */
    formDefinition?: FormDefinition | null | undefined;
    clearCallback?: (() => void) | undefined;
}

export interface SchemaState<FORM_DATA> {
    outputFormData: FORM_DATA;
    hasErrors: boolean;
    resetData: boolean;
}

export class CustomSchema<
    FORM_DATA extends CustomFormFields = CustomFormFields
> extends React.Component<SchemaProps<FORM_DATA>, SchemaState<FORM_DATA>> {
    constructor(props) {
        super(props);
        this.state = {
            outputFormData: {} as FORM_DATA,
            hasErrors: false,
            resetData: false
        };
    }

    override render() {
        let CustomForm: React.ReactElement | null = null;

        if (this.props.formDefinition) {
            if (this.props.readOnly) {
                this.props.formDefinition.definition.schema.uiSchema["ui:disabled"] = true;
            }
            CustomForm = (
                <div className="container-fluid v-gap-15">
                    <SchemaForm<FORM_DATA>
                        readOnly={this.props.readOnly}
                        formData={this.props.formEvidence ? this.props.formEvidence.form : null}
                        formDefinition={this.props.formDefinition}
                        onChange={(data, hasErrors) => this.handleChangeForm(data, hasErrors)}
                        resetData={this.state.resetData}
                    />
                </div>
            );
        }

        return <div>{CustomForm}</div>;

        /* NOTE: the order of this in the JSX is important.
         * See https://facebook.github.io/react/docs/transferring-props.html */
    }

    /**
     * Allow calling forms to trigger a reset of the custom form
     */
    public resetData() {
        this.setState(prevState => update(prevState, {resetData: {$set: true}}));
        this.props.clearCallback && this.props.clearCallback();
    }

    /**
     * A more concise approach to the below - this has no typing because we don't need any here.
     */
    private handleChangeForm(data: FORM_DATA, hasErrors: boolean) {
        this.setState(prevState =>
            update(prevState, {
                outputFormData: {$set: data},
                hasErrors: {$set: hasErrors},
                resetData: {$set: false}
            })
        );
    }
}

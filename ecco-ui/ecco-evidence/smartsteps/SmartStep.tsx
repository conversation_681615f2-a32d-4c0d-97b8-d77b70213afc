import {
    Box,
    Checkbox,
    FormControl,
    FormControlLabel,
    Grid,
    IconButton,
    InputLabel,
    List,
    ListItem,
    MenuItem,
    Select,
    TextField,
    Typography,
    withStyles
} from "@eccosolutions/ecco-mui";
import * as React from "react";
import {FC, useCallback, useState} from "react";
import {
    SelectList,
    datePickerInput,
    stringFromHtmlInput,
    textInput,
    dropdownList
} from "ecco-components-core";
import {
    EditOff,
    SmartStepLinkLogo,
    SmartStepPlusFadedLogo,
    SmartStepPlusLogo,
    SmartStepStarFadedLogo,
    SmartStepStarLogo,
    SmartStepUnlinkLogo,
    SmartStepCommentOnlyLogo,
    useServicesContext
} from "ecco-components";
import {
    isForcedRelatedState,
    smartStepInitLookup,
    SmartStepProps,
    SmartStepReadProps,
    SmartStepRemoveProps,
    SmartStepState,
    updateRelatedState
} from "./SmartStepRoot";
import {
    ActionComponent,
    OutcomeComponent,
    SmartStepDisplaySymbol,
    SmartStepStatus,
    SmartStepStatusPlanName
} from "ecco-dto";
import {AddIcon, DeleteIcon, EditIcon, EventIcon} from "@eccosolutions/ecco-mui-controls"; // NB UnfoldMore / UnfoldLess possibles
import {EccoDate} from "@eccosolutions/ecco-common";
import {useEvidencePageContext} from "../EvidencePageRoot";
import {Uuid} from "@eccosolutions/ecco-crypto";



/*******
 * Label
 */

/*
<Typography component="span">This is a span element with MUI Typography</Typography>
function TypographySpan(props: OverridableComponent<TypographyTypeMap>) {
    return (
        <Typography component="span">
            {props.props.children}
        </Typography>
    );
}*/
const labelColour = {
    color: "#555",
    /*fontStyle: "italic",*/
    /*fontWeight: "bold",*/
    fontSize: "smaller"
};
const TypographyColour = withStyles({
    root: {
        ...labelColour,
        display: "flex", // allows vertical align - https://stackoverflow.com/a/56342694
        flexDirection: "column",
        justifyContent: "center"
    }
})(Typography);

const LabelText: FC<{text: string; alignRight?: boolean}> = props => {
    return (
        <TypographyColour style={props.alignRight ? {textAlign: "right"} : undefined}>
            {props.text}
        </TypographyColour>
    );
};


/*******
 * Name
 */

const SmartStepDefName: FC<{actionDef: ActionComponent | null}> = props => {
    return (
        <TypographyColour style={{fontWeight: "bold"}}>
            {props.actionDef?.getName()}
        </TypographyColour>
    );
};


/*********
 * Status
 */

const symbolImages: {symbol: SmartStepDisplaySymbol; img: string}[] = [
    {
        symbol: SmartStepDisplaySymbol.FullPlus,
        img: SmartStepPlusLogo
    },
    {
        symbol: SmartStepDisplaySymbol.FadedPlus,
        img: SmartStepPlusFadedLogo
    },
    {
        symbol: SmartStepDisplaySymbol.FullStar,
        img: SmartStepStarLogo
    },
    {
        symbol: SmartStepDisplaySymbol.FadedStar,
        img: SmartStepStarFadedLogo
    },
    {
        symbol: SmartStepDisplaySymbol.CommentOnly,
        img: SmartStepCommentOnlyLogo
    }
];
function symbolSrc(symbol: SmartStepDisplaySymbol): string {
    let img;
    for (let key in symbolImages) {
        if (symbolImages[key].symbol == symbol) {
            img = symbolImages[key].img;
            // get the escaped path of the icon
            return img;
        }
    }
    throw new Error(`missing smart step symbol icon for ${symbol}`);
}
export const SmartStepStatusSymbol: FC<{
    onClick: () => void;
    symbol: SmartStepDisplaySymbol;
}> = props => {
    return (
        <IconButton
            color="default"
            size="small"
            onClick={props.onClick}
            style={{fontSize: "small"}}
        >
            <img
                alt={"status"}
                src={symbolSrc(props.symbol)}
                height={24}
                width={24}
            />
        </IconButton>
    );
};
function smartStepStatusSymbolSrc(props: SmartStepReadProps): SmartStepDisplaySymbol {
    return props.init.statusTransitions.getDisplaySymbol(
        props.init.initState.status!,
        props.state.status!
    );
}

const SmartStepStatusSelect = (props: SmartStepProps) => {
    const options = props.init.statusTransitions.getAllowedStatuses(
        props.init.initState.status || props.state.transientStatus || null
    );
    const labelId = `${props.init.initState.controlUuid.toString()}-status-label`;
    return (
        <FormControl style={{width: "100%"}}>
            <InputLabel id={labelId}>status</InputLabel>
            <Select
                labelId={labelId}
                label={"status"}
                fullWidth={true}
                placeholder={"status"}
                value={props.state.status}
                onChange={(
                    event: React.ChangeEvent<{name?: string; value: unknown}>,
                    child: React.ReactNode
                ) => props.stateSetter({status: parseInt(event.target.value as string)})}
            >
                {options.map(o => {
                    const src = symbolSrc(
                        props.init.statusTransitions.getDisplaySymbol(
                            props.init.initState.status!,
                            o
                        )
                    );
                    const label = SmartStepStatusPlanName[o];
                    return (
                        <MenuItem key={`status-${o.toString()}`} value={o.toString()}>
                            {/* using ListItemIcon, ListItemText doesn't inline the picture - https://stackoverflow.com/a/65179219 */}
                            <div style={{display: "flex", alignItems: "center"}}>
                                <img
                                    alt={"status"}
                                    src={src}
                                    height={24}
                                    width={24}
                                    style={{verticalAlign: "middle"}}
                                />
                                <div>{label}</div>
                            </div>
                        </MenuItem>
                    );
                })}
            </Select>
        </FormControl>
    );
};


/*********
 * New SmartStep
 */

const SmartStepActionDefSelect = (props: SmartStepProps) => {
    const sessionData = props.init.context.features;
    const outcome: OutcomeComponent | undefined =  sessionData.getAnyOutcomeByIdAsComponent(props.init.initState.transientOutcomeDefId!);
    const actions =
        outcome
            ?.getActionGroups()
            .map(ag => ag.getActions())
            .reduce((r, x) => r.concat(x), []) // flatMap
            .filter(a => !sessionData.isAnyActionByIdDisabled(a.getId()))
            .map(a => {
                // override disabled, as Select (react-select) appears to think it's disabled
                const name = sessionData.describeAnyAction(a, false);
                return {
                    id: a.getId(),
                    name: name,
                    disabled: false
                };
            }) || [];

    const labelId = `${props.init.initState.controlUuid.toString()}-actionDef-label`;
    return (
        <SelectList
            key={labelId}
            isMulti={false}
            isDisabled={false}
            placeholder={"choose an area..."}
            createNew={false}
            getOptionLabel={a => a.name}
            getOptionValue={a => a.id.toString()}
            value={actions.find(a => a.id == props.state.actionDefId)}
            options={actions}
            onChange={v =>
                props.stateSetter({
                    actionDefId: v ? (v as {name: string; id: number}).id : undefined,
                    transientStatus: SmartStepStatus.CommentOnly, // we'd like to use initState but emitCommand needs no status
                    status: SmartStepStatus.CommentOnly // and we need initStatus to know the getAllowedStatuses
                })
            }
        />
    );
};


/************
 * Goal name
 */

// display if this.controlFeatures.showGoalName(outcomeId)
const SmartStepGoalName = (props: {
    state: SmartStepState;
    stateSetter: (update: Partial<SmartStepState>) => void;
    label: string;
}) => {
    const {state, stateSetter, label} = props;
    // limit 384 chars
    return (
        <>{textInput("goalName", label, stateSetter, state, undefined, undefined, false, 384)}</>
    );
};


/*******
 * Link
 */

// display if this.controlFeatures.showLink(outcomeId)
// click -> updateRelatedUI(directClick)
const SmartStepLink = (props: SmartStepProps) => {
    const onClick = () => {
        props.stateSetter({
            relatedDirectClick: !props.state.relatedDirectClick
        });
    };

    const linkPic = props.state.related ? SmartStepLinkLogo : SmartStepUnlinkLogo;
    return (
        <>
            <IconButton color="default" size="small" onClick={onClick} style={{fontSize: "small"}}>
                <img
                    alt={"status"}
                    src={linkPic}
                    height={24}
                    width={24}
                    style={{verticalAlign: "middle"}}
                />
            </IconButton>
        </>
    );
}


/*********
 * Expiry
 */

// display if this.controlFeatures.showExpiry(outcomeId)
interface ExpiryWrapper {
    expiryDate: EccoDate | null;
}
const SmartStepExpiryDate = (props: SmartStepProps) => {
    const {state, stateSetter, init} = props;

    const formatDateIn: ExpiryWrapper = {
        expiryDate: EccoDate.parseIso8601(state.expiryDate)
    };
    const formatDateOut = (wrapper: ExpiryWrapper) => {
        stateSetter({
            expiryDate: wrapper.expiryDate
                ? wrapper.expiryDate.formatIso8601CatchingInvalidAsNull()
                : null
        });
    };

    return (
        <>{datePickerInput("expiryDate", "expiry", formatDateOut, formatDateIn, false, false)}</>
    );
};


/*********
 * Target
 */
interface TargetWrapper {
    targetDate: EccoDate | null;
}
const SmartStepTargetDate = (props: SmartStepProps) => {
    const {state, stateSetter, init} = props;
    const [showDatePickerIcon, setShowDatePickerIcon] = useState(!state.targetDate);

    const formatDateIn: TargetWrapper = {
        targetDate: EccoDate.parseIso8601(state.targetDate)
    };
    const formatDateOut = (wrapper: TargetWrapper) => {
        stateSetter({
            targetDate: wrapper.targetDate
                ? wrapper.targetDate.formatIso8601CatchingInvalidAsNull()
                : null
        });
    };

    return (
        <>
            {showDatePickerIcon && (
                <IconButton
                    color="default"
                    size="small"
                    onClick={() => setShowDatePickerIcon(false)}
                    style={{fontSize: "small"}}
                    title="target date"
                >
                    <EventIcon />
                </IconButton>
            )}
            {!showDatePickerIcon &&
                datePickerInput("targetDate", "target", formatDateOut, formatDateIn, false, false)}
        </>
    );
};


/******************
 * Target schedule
 */

// const SmartStepTargetSchedule = () => {
// }


/************
 * Goal Plan
 */

const SmartStepGoalPlan = (props: SmartStepProps & {label: string}) => {
    const {state, stateSetter} = props;
    return (
        <TextField
            name={`${state.controlUuid}-goalPlan`}
            label={props.label}
            /*type={type}*/
            fullWidth={true}
            /*size={options.size}*/
            /*placeholder={"goal plan..."}*/
            /*disabled={options.disabled}*/
            /*rowsMax={options.rows}*/
            /*required={options.required}*/
            /*error={valid == "error"}*/
            multiline={true}
            /*rows={options.rows}*/
            onChange={event => stateSetter({...state, goalPlan: stringFromHtmlInput(event.target)})}
            value={state.goalPlan}
        />
    );
    //return <>{textArea("goalPlan", "comment", stateSetter, state, undefined, false)}</>;
};


/************
 * Score
 */

const SmartStepScore = (props: SmartStepProps & {label: string}) => {
    const {state, stateSetter} = props;

    const taskName = props.init.initData.evidenceDef.getTaskName();
    const serviceType = props.init.initData.configResolver.getServiceType();
    const scoreListName = serviceType.getTaskDefinitionSetting(taskName, "scoreListName");
    if (!scoreListName) {
        return null;
    }

    const scores = props.init.initData.sessionData.getListDefinitionEntriesByListName(scoreListName,
            undefined, state.score != null ? state.score : true);
    const scoresToOptions = scores.map(s => {
        return {
            id: s.getId(),
            name: s.getDisplayName()
        }
    });
    return (
        <>
            {dropdownList(
                props.label,
                stateSetter,
                state,
                "score",
                scoresToOptions,
                undefined,
                false,
                false
            )}
        </>
    );
};


/************
 * Reason
 */

const SmartStepReason = (props: SmartStepProps & {label: string}) => {
    const {state, stateSetter} = props;

    const reasonListName = "actionNotCompleteReason";
    const reasons = props.init.initData.sessionData.getListDefinitionEntriesByListName(reasonListName,
                                                                                      undefined, state.statusChangeReasonId != null ? state.statusChangeReasonId : true);
    const reasonsToOptions = reasons.map(s => {
        return {
            id: s.getId(),
            name: s.getDisplayName()
        }
    });
    return (
        <>
            {dropdownList(
                props.label,
                stateSetter,
                state,
                "statusChangeReasonId",
                reasonsToOptions,
                undefined,
                false,
                true
            )}
        </>
    );
};


/************
 * Custom
 */



/*********
 * SubSmartStep Wrapper
 */

// rather than pass in the props, we get from the context in a Wrapper
// component, so that we can still test the layout directly
const SubStepWrapper = (props: {parentControlUuid: Uuid}) => {
    const {state: statePage} = useEvidencePageContext();

    const parent = statePage.smartSteps.find(s => s.controlUuid.equals(props.parentControlUuid))!;

    // TODO ordering
    const smartStepsForParent = statePage.smartSteps.filter(s =>
        s.parentControlUuid?.equals(props.parentControlUuid)
    );

    return parent.editing ? (
        <List style={{width: "100%"}}>
            {smartStepsForParent.map(s => (
                <SubStepEditLayout key={`sub-layout-${s.controlUuid}`} state={s} />
            ))}
        </List>
    ) : (
        <ul>
            {smartStepsForParent.map(s => (
                <SubStepViewLayout key={`sub-layout-${s.controlUuid}`} state={s} />
            ))}
        </ul>
    );
};


/*********
 * SubSmartStep Layout
 */

const checkBoxStyles = withStyles({
    root: {
        "&$checked": {
            color: "#aaa" // #2196f3
        }
    },
    checked: {}
});
const SubStepCheckbox = (checkBoxStyles)(Checkbox);

const SubStepViewLayout = (props: {state: SmartStepState}) => {
    return (
        <li>
            <span
                style={
                    props.state.status == SmartStepStatus.Achieved
                        ? {textDecoration: "line-through"}
                        : undefined
                }
            >
                {props.state.goalName}
            </span>
        </li>
    );
};

const SubStepNewLayout = (props: {parentControlUuid: Uuid}) => {
    const {init, dispatch} = useEvidencePageContext();

    const addSubStep = useCallback((parentControlUuid: Uuid) => {
        dispatch({
            type: "addSubStep",
            initData: init.initData,
            parentControlUuid: parentControlUuid
        });
    }, []);

    return (
        <IconButton
            style={{paddingTop: 0, paddingBottom: "16px", color: "#aaa"}}
            onClick={() => addSubStep(props.parentControlUuid!)}
        >
            <AddIcon />
        </IconButton>
    );
};

const SubStepEditLayout = (props: {state: SmartStepState}) => {
    const {dispatch, init, state: pageState} = useEvidencePageContext();
    const stateSetter = useCallback((s: Partial<SmartStepState>) => {
        dispatch({
            type: "updateSmartStep",
            data: s,
            controlUuid: props.state.controlUuid
        });
    }, []);

    const changeStatus = (checked: boolean) => {
        stateSetter({status: checked ? SmartStepStatus.Achieved : SmartStepStatus.WantToAchieve});
    };

    const smartStepInit = smartStepInitLookup(
        init.initSmartSteps,
        pageState.smartStepsProvisioned,
        props.state.controlUuid
    );
    const taskName = smartStepInit.initData.evidenceDef.getTaskName();
    const serviceType = smartStepInit.initData.configResolver.getServiceType();

    // NB may need labels per hierarchy - see props.init.controlFeatures
    const goalNameLabel =
        smartStepInit.initData.configResolver
            .getServiceType()
            .getTaskDefinitionSetting(taskName, "goalNameLabel") || "task";
    let hasTarget = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponentsSubActions1",
        "target"
    );
    const hasGoalPlan = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponentsSubActions1",
        "comment"
    );
    const goalPlanLabel =
        smartStepInit.initData.configResolver
            .getServiceType()
            .getTaskDefinitionSetting(taskName, "goalPlanLabel") || "detail";

    return (
        <>
            <ListItem>
                <Grid container>
                    <Grid item xs={1}>
                        <FormControlLabel
                            control={
                                <SubStepCheckbox
                                    checked={props.state.status == SmartStepStatus.Achieved}
                                    onChange={event => changeStatus(event.target.checked)}
                                />
                            }
                            label={""}
                        />
                    </Grid>

                    <Grid item xs={hasTarget ? 6 : 11}>
                        <SmartStepGoalName
                            state={props.state}
                            stateSetter={stateSetter}
                            label={goalNameLabel}
                        />
                    </Grid>

                    {hasTarget && (
                        <Grid item xs={5}>
                            <SmartStepTargetDate
                                init={smartStepInit}
                                state={props.state}
                                stateSetter={stateSetter}
                            />
                        </Grid>
                    )}

                    {hasGoalPlan && (
                        <>
                            <Grid item xs={1}>
                                &nbsp;
                            </Grid>
                            <Grid item xs={11}>
                                <SmartStepGoalPlan
                                    init={smartStepInit}
                                    state={props.state}
                                    stateSetter={stateSetter}
                                    label={goalPlanLabel}
                                />
                            </Grid>
                        </>
                    )}

                    {/*<Grid item xs={12}>
                <Typography>
                    {props.state.targetDate
                        ? EccoDate.parseIso8601(props.state.targetDate)?.formatShort()
                        : null}
                </Typography>
            </Grid>*/}
                </Grid>
            </ListItem>

            {/*{hasGoalPlan && (
            <ListItem>
                <Grid item xs={12}>
                    <SmartStepGoalPlan
                            init={smartStepInit}
                            state={props.state}
                            stateSetter={stateSetter}
                            label={goalPlanLabel}
                    />
                </Grid>
            </ListItem>
        )}*/}
        </>
    );
};

/*********
 * Layout
 */

/**
 * NB SubStepWrapper grabs the context
 */
export const SmartStepViewLayout = (props: SmartStepReadProps & {setEditing: () => void}) => {
    const {state: pageState, dispatch} = useEvidencePageContext();

    // we can set a state change of 'link'
    const stateSetter = useCallback((s: Partial<SmartStepState>) => {
        dispatch({
            type: "updateSmartStep",
            data: s,
            controlUuid: props.state.controlUuid
        });
    }, []);

    const taskName = props.init.initData.evidenceDef.getTaskName();
    const serviceType = props.init.initData.configResolver.getServiceType();
    const hasStatus = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponents",
        "status"
    );
    const hasExpiry = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponents",
        "expiry"
    );

    const showGoalPlan =
        props.init.initData.readOnly || props.state.status == SmartStepStatus.CommentOnly; // if printable page, else don't show in 'view' mode unless a comment

    const hasLink = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponents",
        "link"
    );

    return (
        <Box border={1} padding={2} borderColor={"#ddd"} borderRadius={8}>
            <Grid container direction="row">
                <Grid container>
                    <Grid item xs={9}>
                        {props.state.actionDefId && (
                            <SmartStepDefName
                                actionDef={props.init.initData.sessionData.getAnyActionById(
                                    props.state.actionDefId
                                )}
                            />
                        )}
                    </Grid>
                    <Grid item xs={3}>
                        <Box display="flex" justifyContent="flex-end">
                            {hasStatus && props.state.status && (
                                <>
                                    <LabelText
                                        text={SmartStepStatusPlanName[props.state.status]}
                                        alignRight={true}
                                    />
                                    <img
                                        alt={"status"}
                                        src={symbolSrc(smartStepStatusSymbolSrc(props))}
                                        width={30} /* we're struggling with 24 */
                                    />
                                </>
                            )}
                            {pageState.pageEditing && (
                                <IconButton
                                    color="default"
                                    size="small"
                                    onClick={props.setEditing}
                                    style={{fontSize: "small"}}
                                >
                                    <EditIcon />
                                </IconButton>
                            )}
                        </Box>
                    </Grid>
                </Grid>

                <Grid item xs={12}>
                    <span>{props.state.goalName}</span>
                </Grid>
                <Grid item xs={12}>
                    <SubStepWrapper parentControlUuid={props.state.controlUuid} />
                </Grid>
                {props.state.targetDate && (
                    <Grid item xs={6}>
                        <span style={{...labelColour, paddingRight: "5px"}}>target</span>
                        <span>{EccoDate.parseIso8601(props.state.targetDate)?.formatShort()}</span>
                    </Grid>
                )}
                {props.state.expiryDate && (
                    <Grid item xs={6}>
                        <span style={{...labelColour, paddingRight: "5px"}}>expiry</span>
                        <span>{EccoDate.parseIso8601(props.state.expiryDate)?.formatShort()}</span>
                    </Grid>
                )}
                {showGoalPlan && (
                    <Grid item xs={12}>
                        <span>{props.state.goalPlan}</span>
                    </Grid>
                )}
                {/*hasLink && (
                    <Grid item xs={12}>
                        <SmartStepLink
                            init={props.init}
                            state={props.state}
                            stateSetter={stateSetter}
                        />
                    </Grid>
                )}{" "}*/}

                {/*NB not doing createActivityInterest*/}
            </Grid>
        </Box>
    );
};

// select the actionDefId first before editing
export const SmartStepNewLayout = (props: SmartStepProps & {remove: () => void}) => {
    return (
        <Box border={1} padding={3} borderColor={"#ddd"} borderRadius={8}>
            <Grid container direction="row" spacing={1}>
                <Grid item xs={8}>
                    <SmartStepActionDefSelect
                        init={props.init}
                        state={props.state}
                        stateSetter={props.stateSetter}
                    />
                </Grid>
                <Grid item xs={4}>
                    <Box display="flex" justifyContent="flex-end">
                        <IconButton
                            color="default"
                            size="small"
                            onClick={() => props.remove()}
                            style={{fontSize: "small"}}
                        >
                            <DeleteIcon />
                        </IconButton>
                    </Box>
                </Grid>
            </Grid>
        </Box>
    );
};

export const SmartStepEditLayout = (
    props: SmartStepProps & {setEditing: () => void; remove: () => void}
) => {
    const isTransient = props.state.actionInstanceUuid == null;

    const taskName = props.init.initData.evidenceDef.getTaskName();
    const serviceType = props.init.initData.configResolver.getServiceType();
    // NB hide the status even if one is set
    const hasStatus = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponents",
        "status"
    );

    let hasGoalName = true;
    const hasGoalPlan = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponents",
        "comment"
    );

    let goalNameLabel = serviceType.getTaskDefinitionSetting(taskName, "goalNameLabel") || "goal";
    let goalPlanLabel = serviceType.getTaskDefinitionSetting(taskName, "goalPlanLabel") || "detail";
    let hasTarget = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponents",
        "target"
    );
    let hasExpiry = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponents",
        "expiry"
    );
    const hasLink = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponents",
        "link"
    );
    let scoreLabel = serviceType.getTaskDefinitionSetting(taskName, "scoreLabel") || "score";
    let hasScore = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponents",
        "score"
    );
    let reasonLabel = "reason";
    let hasReason = serviceType.taskDefinitionSettingHasFlag(
        taskName,
        "showActionComponents",
        "statusChangeReason"
    );
    // as per SupportInstanceControl - don't show if already achieved, but we may want to change this
    hasReason =
        hasReason &&
        !(
            props.state.status == SmartStepStatus.Achieved ||
            props.state.status == SmartStepStatus.CommentOnly
        );


    // NB show existing sub steps regardless, just stop new ones
    let hasSubStepsNew = serviceType.taskDefinitionSettingHasFlag(taskName, "showSubActions", "y");
    let hasGoalCustomFields = serviceType.getTaskDefinitionSetting(taskName, "goalPlanCustom");

    // if status 'comment', then hide some stuff
    const isCommentOnly = props.state.status == SmartStepStatus.CommentOnly;
    if (isCommentOnly) {
        hasGoalName = props.state.goalName != null; // show if we have data
        hasExpiry = props.state.expiryDate != null; // show if we have data
        hasScore = props.state.score != null; // show if we have data
        hasTarget = props.state.targetDate != null; // show if we have data
        hasReason = props.state.statusChangeReasonId != null; // show if we have data
        hasSubStepsNew = false;
        goalPlanLabel = "comment";
    }

    return (
        <Box border={1} padding={3} borderColor={"#ddd"} borderRadius={8}>
            <Grid container direction="row" spacing={1}>
                <Grid item xs={8}>
                    {props.state.actionDefId && (
                        <SmartStepDefName
                            actionDef={props.init.initData.sessionData.getAnyActionById(
                                props.state.actionDefId
                            )}
                        />
                    )}
                </Grid>
                <Grid item xs={4}>
                    <Box display="flex" justifyContent="flex-end">
                        {hasStatus && (
                            <Grid item xs={8}>
                                <SmartStepStatusSelect
                                    init={props.init}
                                    state={props.state}
                                    stateSetter={props.stateSetter}
                                />
                            </Grid>
                        )}
                        {isTransient && (
                            <IconButton
                                color="default"
                                size="small"
                                onClick={() => props.remove()}
                                style={{fontSize: "small"}}
                            >
                                <DeleteIcon />
                            </IconButton>
                        )}
                        <IconButton
                            color="default"
                            size="small"
                            onClick={() => props.setEditing()}
                            style={{fontSize: "small"}}
                        >
                            <img alt="edit off" src={EditOff} width={24} height={24} />
                        </IconButton>
                    </Box>
                </Grid>
                {hasGoalName && (
                    <Grid item xs={12}>
                        <SmartStepGoalName
                            state={props.state}
                            stateSetter={props.stateSetter}
                            label={goalNameLabel}
                        />
                    </Grid>
                )}
                <SubStepWrapper parentControlUuid={props.state.controlUuid} />
                {hasSubStepsNew && <SubStepNewLayout parentControlUuid={props.state.controlUuid} />}
                {hasGoalPlan && (
                    <Grid item xs={12}>
                        <>
                        {hasGoalCustomFields &&
                            /*<CustomSchema ref={onComponentRef}
                                          formEvidence={formEvidence}
                                          formDefinition={formDefinition}
                                          readOnly={readOnlyFinal}
                                          clearCallback={clearCallback}
                            />*/
                        }
                        {!hasGoalCustomFields &&
                            <SmartStepGoalPlan
                               init={props.init}
                                state={props.state}
                                stateSetter={props.stateSetter}
                                label={goalPlanLabel}
                            />
                        }
                        </>
                    </Grid>
                )}
                {hasScore && (
                    <Grid item xs={6}>
                        <SmartStepScore
                            label={scoreLabel}
                            init={props.init}
                            state={props.state}
                            stateSetter={props.stateSetter}
                        />
                    </Grid>
                )}
                {hasTarget && (
                    <Grid item xs={6}>
                        <SmartStepTargetDate
                            init={props.init}
                            state={props.state}
                            stateSetter={props.stateSetter}
                        />
                    </Grid>
                )}
                {hasExpiry && (
                    <Grid item xs={6}>
                        <SmartStepExpiryDate
                            init={props.init}
                            state={props.state}
                            stateSetter={props.stateSetter}
                        />
                    </Grid>
                )}
                {hasReason && (
                    <Grid item xs={12}>
                        <SmartStepReason
                            label={reasonLabel}
                            init={props.init}
                            state={props.state}
                            stateSetter={props.stateSetter}
                        />
                    </Grid>
                )}
                {hasLink && (
                    <Grid item xs={12}>
                        <SmartStepLink
                            init={props.init}
                            state={props.state}
                            stateSetter={props.stateSetter}
                        />
                    </Grid>
                )}{" "}
                <Grid item xs={12}></Grid>
                {/*NB not doing createActivityInterest*/}
            </Grid>
        </Box>
    );
};

/**
 * External entry point to the layout
 */
export const SmartStep: FC<SmartStepRemoveProps> = props => {
    const {sessionData} = useServicesContext();

    // intercept the state change and update the 'related' state as required
    // as a callback to improve performance
    const stateSetterDecorator = useCallback(
        (props: SmartStepProps, update: Partial<SmartStepState>) => {
            const relatedDirectClickNow =
                update.related !== null &&
                update.related !== undefined &&
                update.related != props.state.related;
            const stateNow = {...props.state, ...update};
            const forceRelated = isForcedRelatedState(sessionData, props.init.initState, stateNow);
            const related =
                stateNow.relatedDirectClick ||
                updateRelatedState(relatedDirectClickNow, stateNow.related || false, forceRelated);
            const s = {...stateNow, related: related};
            props.stateSetter(s);
        },
        []
    );
    const stateRemove = useCallback(() => props.remove(), []);

    return !props.state.editing ? (
        <SmartStepViewLayout
            state={props.state}
            init={props.init}
            setEditing={() => stateSetterDecorator(props, {editing: !props.state.editing})}
        />
    ) : !props.state.actionDefId ? (
        <SmartStepNewLayout
            init={props.init}
            state={props.state}
            stateSetter={props.stateSetter}
            remove={() => stateRemove()}
        />
    ) : (
        <SmartStepEditLayout
            state={props.state}
            stateSetter={s => stateSetterDecorator(props, s)}
            init={props.init}
            setEditing={() => stateSetterDecorator(props, {editing: !props.state.editing})}
            remove={() => stateRemove()}
        />
    );
    /*return props.init.initData.readOnly ? (
        <SmartStepViewLayout state={props.state} init={props.init} />
    ) : (
        <SmartStepLayout state={props.state} stateSetter={stateSetterDecorator} init={props.init} />
    );*/
};
SmartStep.whyDidYouRender = true;
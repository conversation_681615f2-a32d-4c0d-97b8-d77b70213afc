# Collapsible Evidence Attachments

This document describes the `CollapsibleEvidenceAttachments` component that provides a space-efficient way to show evidence file upload functionality.

## Overview

The `CollapsibleEvidenceAttachments` component replaces the full-width evidence attachments section with a compact icon that expands to show the upload interface when clicked. This saves screen real estate while keeping the functionality easily accessible.

## Features

- **Compact Icon**: Shows as a small attachment icon in the right sidebar
- **Expandable Panel**: Clicking the icon reveals the full upload interface in a floating panel
- **File Count Badge**: Optionally shows the number of attached files as a badge on the icon
- **Responsive**: The expanded panel is positioned absolutely to avoid layout shifts
- **Accessible**: Proper ARIA labels and keyboard navigation support

## Usage

### Basic Usage

```tsx
import { CollapsibleEvidenceAttachments } from "ecco-evidence";

// In your evidence page component
<CollapsibleEvidenceAttachments />
```

### With Options

```tsx
<CollapsibleEvidenceAttachments 
    defaultExpanded={false}
    showFileCount={true}
/>
```

### Compact Version

For very tight spaces, use the compact version:

```tsx
import { CompactEvidenceAttachments } from "ecco-evidence";

<CompactEvidenceAttachments defaultExpanded={false} />
```

## Props

### CollapsibleEvidenceAttachments

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `defaultExpanded` | `boolean` | `false` | Whether the panel starts expanded |
| `showFileCount` | `boolean` | `true` | Whether to show file count badge |

### CompactEvidenceAttachments

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `defaultExpanded` | `boolean` | `false` | Whether the panel starts expanded |

## Integration

The component is integrated into the `EvidencePagePlanLayout` in the right sidebar area, alongside the `EvidencePageFilter`. It only appears when the page is in editing mode.

### Location in Layout

```tsx
<Grid item xs={12} sm={2} md={1} container justify="flex-end">
    <EvidencePageFilter />
    {state.pageEditing && (
        <CollapsibleEvidenceAttachments defaultExpanded={false} showFileCount={true} />
    )}
</Grid>
```

## Styling

The component uses Material-UI's styling system with the following key styles:

- **Container**: Positioned relatively to contain the absolute-positioned panel
- **Icon Button**: Primary color with hover effects
- **Expanded Panel**: Floating panel with shadow, positioned to the right of the icon
- **Badge**: Small, compact badge showing file count

## Behavior

1. **Collapsed State**: Shows only the attachment icon
2. **Expanded State**: Shows the full `EvidenceFileUploadExample` component in a floating panel
3. **Click to Toggle**: Clicking the icon toggles between collapsed and expanded states
4. **Outside Click**: The panel remains open until explicitly closed (no auto-close on outside click)

## Future Enhancements

- **File Count Integration**: Connect to actual file count from evidence context/API
- **Auto-close**: Add option to close panel when clicking outside
- **Animation**: Enhanced expand/collapse animations
- **Mobile Optimization**: Better mobile layout and touch interactions

## Testing

The component includes comprehensive tests covering:
- Default collapsed state
- Expand/collapse functionality
- File count badge display
- Accessibility features

Run tests with:
```bash
npm test -- CollapsibleEvidenceAttachments.test.tsx
```

import $ = require("jquery");
import {ActionsChangedCallback, ActionState, bus, EventBus, FormContent, ModalStyle} from "@eccosolutions/ecco-common";
import DialogContent from "../controls/DialogContent";
import {DomElementContainer, EccoTheme, ModalFormWithActionStates} from "ecco-components-core";
import * as React from "react";
import {render, unmountComponentAtNode} from "react-dom";

/**
 * To be used in conjunction with DialogContentAdapter (because this doesn't handle getFooter)
 */
function dialogToFormContentAdapter (dialogContent: DialogContent): FormContent {
    return {
        getTitle(): string {
            const title = dialogContent.getTitle();
            return typeof title === "string" ? title : title.text();
        },
        setOnFinished(callback: () => void): void {
            dialogContent.setOnFinished(callback);
        },
        registerActionsChangeListener(updateActions: (actions: ActionState[]) => void): void {
            dialogContent.registerActionsChangeListener(updateActions);
        },
        getModalStyle(): ModalStyle {
            return dialogContent.getModalClass ? dialogContent.getModalClass() : ""
        }
    }
}

/**
 * This provides some weird glue where we want to show a form in a
 * React MUI modal Dialog, but the form is JQuery/DOM based and
 * we want to show it from JQuery/DOM context.
 * This provides us a bridge where we can start migrating to React
 * NB onCancel is assumed to be unmount, which is passed to ModalForm, _ModalForm and <ActionButtons> withCloseCancel
 * NB DialogContent getFooter will be ignored - the adapter doesn't convert the element to a listener
 * NB Please use registerActionsChangeListener NOT getFooter if using this method
 * NB No callback method to know when close icon is pressed - use actionStates approach (42929396)
 */
export function showFormInModalDom(
    form: DialogContent,
    readOnly = false,
    mountPoint: HTMLElement = document.createElement("div"), // NOTE: ReactDom.render appends this to <body> but is undocumented behaviour AFAICT
    withCloseOrCancel = true
) {
    // unmount is passed to onFinished which gets added to the forms setOnFinished bus,
    // and the dialog close icon onClose and the Action's onFinished (when provided with default buttons).
    // The underlying form will then call its 'setOnFinished' code including the unmount.
    // If your underlying code is simply 'new DialogContentAdapter' wrapper
    // (which is the case with 'showModalWithActions') then it needs to trigger the call itself.
    // However, this is tricky to trigger in the 'close icon' because the actions buttons do it themselves
    // (in showFormWithActions), and the form itself is an independent control so manages the save itself.
    // The dialog close icon doesn't have a hook/trigger to help fire the onFinished in 'new DialogContentAdapter'.

    // Initially we tried to solve by hooking into unmount (testing 'form instance of DialogContentAdapter') to then
    // trigger the onFinished code, because that goes to the close icon too - but onFinished also gets pushed to
    // form.onFinished which then calls unmount and fires again creating a circular loop. Therefore we remove onFinished
    // from DialogContentAdapter to avoid confusion (see its notes) and attach directly to DialogContentAdapter's bus.
    const unmount = () => {
        unmountComponentAtNode(mountPoint);
    };
    let onFinish = unmount;
    if (form instanceof DialogContentAdapter) {
        const leaveDialogBusHandler = form.getLeaveDialogBusHandler();
        if (leaveDialogBusHandler != null) {
            leaveDialogBusHandler.addHandler(() => unmount());
            // onFinish now calls unmount and anything else, once
            onFinish = () => leaveDialogBusHandler.fire();
        }
    }

    // onFinished is passed to the form.setOnFinished, any default Action buttons and the onClose cross icon
    render(<EccoTheme prefix="showFormIn">
            <ModalFormWithActionStates
                form={dialogToFormContentAdapter(form)} readOnly={readOnly}
                onFinished={onFinish} withCloseOrCancel={withCloseOrCancel}
            >
                <DomElementContainer content={form.element()[0]}/>
            </ModalFormWithActionStates>
        </EccoTheme>,
    mountPoint);
}

/**
 * NB onCancel is created via showFormInModalDom.unmount, which is passed to ModalForm, _ModalForm and <ActionButtons> withCloseCancel
 * Therefore the form can close itself, but there is no indication to the callee. Use showModalWithActions instead.
 */
export function showModalWithSubmitCancel(title: string, submitText: string, closeLinkTextUNUSED: string,
    $content: $.JQuery, onSubmit: () => void, modalClass?: ModalStyle | undefined) {

    const closeBus = bus<void>();
    const form = new DialogContentAdapter(title, $content, [
        {
            label: submitText,
            onClick: () => { onSubmit(); closeBus.fire(); return Promise.resolve(); },
            style: "primary"}
    ], closeBus, modalClass);

    showFormInModalDom(form);
}

/**
 * This method specifically handles more flexible form action buttons - eg from a wizard.
 *
 * @param actions buttons provided directly
 * @param forceClose for when an action in $content saves the form, and we also need to close the dialog
 * @param onCloseIcon for calling the specific action of 'close' icon - we have no handle to this outside this method
 *                      - this is the only action that we cannot control from this method itself.
 */
export function showModalWithActions(title: string, $content: $.JQuery, actions: ActionState[], modalStyle?: ModalStyle | undefined,
                                     forceClose?: ((trigger: () => void) => void) | undefined) {
    const closeBus = bus<void>();
    // this allows us to close the modal when something inside the form itself need to trigger it - not part of an ActionState
    // alternatively, this could have been done as a hidden ActionState button perhaps
    if (forceClose) {
        const triggerClose = () => {
            closeBus.fire()
        };
        forceClose(triggerClose);
    }

    // we needed this to trigger a close of a dialog when an action was taken
    const actionsThenClose: ActionState[] = actions.map(a => {
        return {
            label: a.label,
            onClick: () => a.onClick(a.label).then(() => closeBus.fire()),
            style: a.style,
            disabled: a.disabled,
            clickedLabel: a.clickedLabel,
            autoDisable: a.autoDisable
        };
    });
    const form = new DialogContentAdapter(title, $content, actionsThenClose, closeBus, modalStyle);

    // In an attempt to trigger the onClose method, we started putting onClose into the DialogContentAdapter 'setOnFinished' hook.
    // However, since DialogContentAdapter is only a wrapper it simply appends to the provided 'closeBus', and caused infinite
    // loops (see unmount comments above). Also, for a wizard, we needed to know when something was closing entirely over closing a page.
    //if (onCloseIcon) {
        //form.setOnFinished(onClose);
    //}
    showFormInModalDom(form, undefined, undefined, false);
}


/** Adapt a DOM element plus ActionState[] into dialog content capable of being shown in a modal
 */
class DialogContentAdapter implements DialogContent {

    private $title = $("<span>");

    /** Callback whenever available actions change state */
    actionChangeCallback: (actions: ActionState[]) => void;

    /**
     * @param actions - actions for buttons/menu. Should call leaveDialogEventBus.fire() when done, if in use
     * @param modalClass - modal-full | modal-sm | modal-lg | "" (for default)
     */
    constructor(formTitle: string,
                private $element: $.JQuery,
                protected actions: ActionState[],
                private leaveDialogEventBus?: EventBus<void> | undefined,
                private modalClass: ModalStyle = "modal-full") {
        this.$title.text(formTitle);
    }

    registerActionsChangeListener(updateActions: ActionsChangedCallback): void {
        this.actionChangeCallback = updateActions;
        this.actionChangeCallback(this.actions);
    }

    getActionsChangedCallback() {
        return this.actionChangeCallback;
    }

    // DialogContent
    public getTitle(): $.JQuery {
        return this.$title;
    }

    public getFooter() {
        return null;
    }

    element(): $.JQuery {
        return this.$element;
    }

    getModalClass(): ModalStyle {
        return this.modalClass;
    }

    setOnFinished(callback: () => void): void {
        // REMOVED!
        // This was simply adding to the leaveDialogEventBus, but this is provided on the constructor
        // so anything pushed through here can access it elsewhere. Well, nothing currently does anyway,
        // so we are asking that anyone creating a new DialogContentAdapter should not use this method.
        // We need to remove this call to avoid some infinite looping - see the commit - although an
        // alternative means could be achieved.
        // We do not throw an error because its part of an interface which is still called for objects
        // that are not pure DialogContentAdapter's.
    }

    getLeaveDialogBusHandler() {
        return this.leaveDialogEventBus;
    }
}

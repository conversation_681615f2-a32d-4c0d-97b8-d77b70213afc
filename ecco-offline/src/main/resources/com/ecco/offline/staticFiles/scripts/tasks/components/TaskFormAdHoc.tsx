import $ = require("jquery");
import BaseAsyncCommandForm from "../../cmd-queue/BaseAsyncCommandForm";
import * as ReactDom from "react-dom"
import * as React from "react"

import {Col, Row} from "react-bootstrap";
import {showFormInModalDom} from "../../components/MUIConverterUtils";

import {EccoDateTime} from "@eccosolutions/ecco-common";
import {EditTaskCommand} from "ecco-commands";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {Operation} from "ecco-commands";
import {datePickerInput, textInput} from "ecco-components-core"

export interface AdHocTask {
    taskInstanceUuid: string;
    serviceRecipientId: number;
    created: string;
    description: string;
    completed?: string | undefined;
    dueDate?: string | undefined;
    assignee?: string | undefined;
}

export class AdHocTaskForm extends BaseAsyncCommandForm<void> {

    private component: TaskDetails;

    constructor(private serviceRecipientId: number, private task: AdHocTask) {
        super("ad-hoc task", false);
        if (!this.task) {
            this.task = {
                serviceRecipientId: serviceRecipientId,
                taskInstanceUuid: null,
                description: null,
                created: EccoDateTime.nowLocalTime().formatIso8601()
            }
        }
    }

    protected override submitForm(): Promise<void> {
        let addOrUpdate: Operation = this.task.taskInstanceUuid ? "update" : "add";
        let taskInstanceUuid = Uuid.parseOrGenerate(this.task.taskInstanceUuid);

        var cmd = new EditTaskCommand(addOrUpdate, this.task.serviceRecipientId, this.task.taskInstanceUuid, null)
            //.changeDescription(this.task.description, this.component.state.description)
            .changeDueDate(EccoDateTime.parseIso8601(this.task.dueDate), this.component.state.dueDate);

        if(cmd.hasChanges()) {
            this.commandQueue.addCommand(cmd);
        }

        return super.submitForm();
    }

    fetchViewData() {
        return Promise.resolve(null);
    }

    override render(stuff: void) {
        this.enableSubmit(); // TODO: should we enable and disable as things change?

        var content = $("<div>");
        this.element().empty().append(content); // Must do this before render otherwise jSig doesn't init

        ReactDom.render( <TaskDetails ref = {c => this.component = c}
                                      task = {this.task}
            />,
            content[0]);

    }
    public static showInModal(serviceRecipientId: number, task?: AdHocTask | undefined, readOnly = false) {
        var form = new AdHocTaskForm(serviceRecipientId, task);
        showFormInModalDom(form, readOnly);
        form.load();
    }
}

interface Props extends React.Props<TaskDetails> {
    task: AdHocTask;
}

interface State {
    description: string;
    dueDate: EccoDateTime;
}
export class TaskDetails extends React.Component<Props, State> {

    constructor(props) {
        super(props);
        let task = this.props.task;
        this.state = {
            description: task.description,
            dueDate: EccoDateTime.parseIso8601(task.dueDate)
        };
    }


    override render() {

        return (
            <Row>
                <Col md={12} lg={10} lgOffset={1}>
                    {this.props.task.created && ("task created " + this.props.task.created)}
                </Col>
                <Col md={12} lg={10} lgOffset={1}>
                    {textInput("description", "description", state => this.setState(state), this.state)}
                </Col>
                <Col md={12} lg={10} lgOffset={1}>
                    {datePickerInput("dueDate", "due date", state => this.setState(state), this.state)}
                </Col>
            </Row>
        );
    }
}

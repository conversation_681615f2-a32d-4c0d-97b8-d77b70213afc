import * as React from "react"

import {
    AsyncServiceRecipientWithEntities,
    CommandSubform,
    CustomFormFields,
    EditTaskSubFormEmbedded,
    getGuidanceUrlCallback,
    SchemaForm,
    SchemaFormRendererProps,
    ServiceRecipientWithEntitiesContext,
    update,
    useServicesContext,
    withCommandForm
} from "ecco-components";
import {possiblyModalForm} from "ecco-components-core";
import {EvidenceGroup, FormDefinition, FormEvidence, ServiceRecipientWithEntities, SessionData, UserGroup} from "ecco-dto";
import {ActionState, EccoDateTime} from "@eccosolutions/ecco-common";
import {
    collectionStateObservableFromSubscription,
    CommandQueue,
    CommandSource,
    FormEvidenceUpdateCommand,
    WorkUuidResolver
} from "ecco-commands";
import {showInCommandForm} from "../../components/CommandForm";
import {showFormInModalDom} from "../../components/MUIConverterUtils";
import CustomFormHistoryListControl from "../../evidence/customform/CustomFormHistoryListControl";
import {EvidenceDef} from "ecco-evidence";
import EvidenceCommentForm from "../../evidence/EvidenceCommentForm";
import {getCustomFormWithSignatureForm} from "./signedCustomForm";
import services = require("ecco-offline-data");
import diff = require("json-patch-gen");
import {FC, ReactElement} from "react";
import {publicComponentAsElementForMui} from "../../components/MUIAsElement";
import {baseURI} from "../../environment";
import {Button} from "react-bootstrap";
import {CustomSchema} from "ecco-evidence/customForm/CustomSchema";

export interface CustomSubformLayoutProps {
    formDefinition: FormDefinition | null;
    formEvidence: FormEvidence<CustomFormFields> | null;
    readOnlyFinal: boolean;
    renderer?: ((props: SchemaFormRendererProps<FormEvidence<CustomFormFields>>) => ReactElement) | undefined;
    clearCallback?: (() => void) | undefined;
    printableLink?: boolean;
    historyLink?: boolean;
    serviceRecipientId: number;
    taskName: string;
    taskHandle?: string | undefined;
    noteQueueSize: number;
    serviceRecipientWithEntities: ServiceRecipientWithEntities;
    onShowHistory: () => void;
    onAddComment: () => void;
    onResetData: () => void;
    onComponentRef: (component: CustomSchema | null) => void;
}

export const CustomSubformLayout: FC<CustomSubformLayoutProps> = ({
    formDefinition,
    formEvidence,
    readOnlyFinal,
    renderer,
    clearCallback,
    printableLink,
    historyLink,
    serviceRecipientId,
    taskName,
    taskHandle,
    noteQueueSize,
    serviceRecipientWithEntities,
    onShowHistory,
    onAddComment,
    onResetData,
    onComponentRef
}) => {
    if (!formDefinition) {
        return null; // no form to show
    }

    const form = renderer
        ? renderer({
            formData: formEvidence,
            formDefinition: formDefinition
        })
        : <CustomSchema ref={onComponentRef}
                        formEvidence={formEvidence}
                        formDefinition={formDefinition}
                        readOnly={readOnlyFinal}
                        clearCallback={clearCallback}
        />

    const $url = `${baseURI}service-recipient/${serviceRecipientId}/task/${taskName}/printable`;

    const Printable = printableLink && <div className="text-center">
        <Button onClick={() => window.open($url.toString(), "_blank")} bsStyle="link">
            printable
        </Button>
    </div>;

    return (<>
        <br/>
        <div className='text-center'>
            {Printable}
            {historyLink &&
                <a className='btn btn-link'
                   onClick={onShowHistory}>
                    history
                </a>
            }
        </div>
        {form}
        <br/><br/>
        <div className='text-center'>
            <br/>
            {!readOnlyFinal &&
                <a className='btn btn-link'
                   onClick={onResetData}>
                    clear form
                </a>
            }
            {!readOnlyFinal &&
                <a className='btn btn-link'
                   onClick={onAddComment}>
                    {noteQueueSize == 0 ? 'add note' : 'redo note'}
                </a>
            }
        </div>
        {!readOnlyFinal &&
            <EditTaskSubFormEmbedded
                taskHandle={taskHandle}
                serviceAllocationId={serviceRecipientWithEntities.serviceAllocationId}
            />
        }
    </>
    )
};

export class CustomFormUtils {

    /*public static showInModal(serviceRecipientId: number, taskName: string, taskNameGroup: string, taskHandle: string, onCompleted?: (() => void) | undefined) {
        showInCommandForm(
            <CustomForm serviceRecipientId={serviceRecipientId} taskName={taskName} taskNameGroup={taskNameGroup} taskHandle={taskHandle}/>
            , onCompleted);
    }*/

    /**
     * Enhance the control on the page.
     */
    public static enhanceForPrinting($element: $.JQuery, onCompleted: () => void, srId: number, taskName: string, taskNameGroup: string, taskHandle?: string | undefined) {
        showInCommandForm(
            getCustomFormWrapper(srId, taskName, taskNameGroup, taskHandle, undefined, 'printable')
                , onCompleted
            , $element[0]);
    }

    public static enhanceForGuidance(elementId: string, formDefinitionUuid: string) {
        publicComponentAsElementForMui(<GuidanceFormLoad formDefinitionUuid={formDefinitionUuid}/>, elementId);
    }
}

// NB CustomFormModal is the default export
type FormProps = {
    serviceRecipientId: number,
    taskName: string,
    taskNameGroup: string,
    /** If provided will ensure that the task status is updated - generally always provided in task list but elsewhere might be null */
    taskHandle?: string | undefined,
    workUuidResolver?: WorkUuidResolver | undefined,
    historyLink?: boolean | undefined
    printableLink?: boolean | undefined
    /** Renderer to use for alternative read-only rendering of form data */
    renderer?: ((props: SchemaFormRendererProps<FormEvidence<CustomFormFields>>) => ReactElement) | undefined
    clearCallback?: (() => void) | undefined
};

// NB Only modal shows the help
type PageType = 'embedded' | 'modal' | 'printable';

/**
 * Must have logic higher up via CommandForm to give us onSave/onCancel behaviour */
export const CustomForm: FC<{serviceRecipientId: number, taskName: string, taskNameGroup: string, taskHandle: string | undefined,
        workUuidResolver?: WorkUuidResolver | undefined, page?: PageType | undefined, readOnly?: boolean | undefined}> = props => {
    return getCustomFormWrapper(props.serviceRecipientId, props.taskName, props.taskNameGroup, props.taskHandle,
            props.workUuidResolver || new WorkUuidResolver(), props.page || 'modal', props.readOnly != undefined ? props.readOnly : false);
};

/**
 * Must have logic higher up via CommandForm to give us onSave/onCancel behaviour
 * @param renderer Overrides default renderer, so we can do concise versions of SchemaForm esp for read-only */
export function getCustomFormWrapper(
        serviceRecipientId: number,
        taskName: string, taskNameGroup: string,
        taskHandle: string | undefined,
        workUuidResolver: WorkUuidResolver | undefined,
        page: PageType,
        readOnly: boolean = false,
        renderer?: ((props: SchemaFormRendererProps<object>) => React.ReactElement) | undefined) {
    return <AsyncServiceRecipientWithEntities srId={serviceRecipientId}>
        <AsyncServiceRecipientWithEntities.Resolved>
            {(context: ServiceRecipientWithEntitiesContext) =>
                getCustomForm(context.serviceRecipient.features, context.serviceRecipient,
                              taskName, taskNameGroup,
                              taskHandle, workUuidResolver, page, readOnly, renderer)
            }
        </AsyncServiceRecipientWithEntities.Resolved>
    </AsyncServiceRecipientWithEntities>
}

/**
 * Must have logic higher up via CommandForm to give us onSave/onCancel behaviour */
function getCustomForm(sessionData: SessionData, serviceRecipient: ServiceRecipientWithEntities,
                       taskName: string, taskNameGroup: string,
                       taskHandle: string | undefined,
                       workUuidResolver: WorkUuidResolver | undefined,
                       page: PageType,
                       readOnly: boolean,
                       renderer?: ((props: SchemaFormRendererProps<object>) => ReactElement) | undefined
) {
    return withCommandForm(commandForm => {

        // attempt to find any guidance
        const guidanceFormDefinitionUuid = serviceRecipient.configResolver.getServiceType().getTaskDefinitionSetting(taskName, "guidanceFormDefinition");
        const guidanceCallback = guidanceFormDefinitionUuid ? getGuidanceUrlCallback(guidanceFormDefinitionUuid) : undefined;

        // Mimics TaskControl.tsx lookupForm
        // NB we only get re-directed if we're loading the whole form, and not if using CustomSubform
        const withSignature = serviceRecipient.configResolver.getServiceType().taskDefinitionSettingHasFlag(taskName,  "captureClientSignature", "y");
        if (withSignature) {
            return page == "printable"
                ? getCustomFormWithSignatureForm(sessionData, serviceRecipient, taskName, taskNameGroup, taskHandle, undefined, false,
                    false, true, true, false, false, guidanceCallback)
                : getCustomFormWithSignatureForm(sessionData, serviceRecipient, taskName, taskNameGroup, taskHandle, undefined, false,
                        page == "modal", undefined, undefined, undefined, undefined, guidanceCallback);
        }

        const readOnlyPage = readOnly || page == 'printable';
        const noFooterButtons = readOnlyPage || page == 'embedded';
        const cancel = noFooterButtons ? null : () => commandForm.cancelForm(); // FIXME: Ensure there is no cancel/close button!
        const save = noFooterButtons ? null : () => commandForm.submitForm(); // FIXME: Ensure there is no save button!
        return possiblyModalForm(
            "",
            page == 'modal', true,
            cancel,
            save,
            readOnlyPage,
            noFooterButtons, // ignore the save buttons, just have close (save is done by accepting the agreement)
            <CustomSubform serviceRecipientId={serviceRecipient.serviceRecipientId}
                           readOnly={readOnlyPage}
                           taskName={taskName}
                           taskNameGroup={taskNameGroup}
                           taskHandle={taskHandle}
                           commandForm={commandForm}
                           workUuidResolver={workUuidResolver}
                           snapshotWorkUuid={undefined}
                           printableLink={!(page == 'printable')}
                           historyLink={!(page == 'printable')}
                           renderer={renderer}
            >
            </CustomSubform>,
            undefined,
            guidanceCallback
        );
    })
}


interface Props extends React.ClassAttributes<CustomSubform>, FormProps {
    readOnly: boolean;
    snapshotWorkUuid?: string | undefined
}

interface State {
    readOnlyFinal: boolean;
    formEvidence: FormEvidence<CustomFormFields> | null;
    formDefinition?: FormDefinition | undefined;
    resetData: boolean;
    workUuidResolver: WorkUuidResolver;
    noteCmdQueue: CommandQueue;
    serviceRecipientWithEntities: ServiceRecipientWithEntities;
    error: Error | null
}

/**
 * Non-CommandSubForm independent evidence modal.
 */
export class CustomSubform extends CommandSubform<Props, State> implements CommandSource {

    private component: CustomSchema;

    constructor(props) {
        super(props);
        this.state = {
            readOnlyFinal: props.readOnly,
            formEvidence: null,
            resetData: false,
            workUuidResolver: props.workUuidResolver ? props.workUuidResolver : new WorkUuidResolver(),
            noteCmdQueue: new CommandQueue(),
            serviceRecipientWithEntities: null!, // FIXME: currently null! because we only dereference it once we've checked it
            error: null
        }
    }

    override componentDidMount() {
        super.componentDidMount();

        services.getReferralRepository().findOneServiceRecipientWithEntities(this.props.serviceRecipientId)
            .then( referral => {

                const editableByGroupsString = referral.configResolver.getServiceType().getTaskDefinitionSettingAsStringCsv(this.props.taskName, "editableByGroups");
                let readOnlyFinal = this.props.readOnly;
                // if there is a chance we need to override readOnly
                if (!readOnlyFinal && editableByGroupsString) {
                    // checking without casting is tricky on statically compiled types
                    const groups = editableByGroupsString.map((grp: string) => grp as UserGroup);
                    readOnlyFinal = !referral.features.hasSomeGroup(groups);
                }

                const workQ = this.props.snapshotWorkUuid
                    ? services.getFormEvidenceRepository().findOneFormEvidenceWorkByServiceRecipientId(this.props.serviceRecipientId,
                        EvidenceGroup.fromName(this.props.taskNameGroup || this.props.taskName), this.props.snapshotWorkUuid)
                    : services.getFormEvidenceRepository().findLatestFormEvidenceSnapshotByServiceRecipientId(this.props.serviceRecipientId, EvidenceGroup.fromName(this.props.taskNameGroup || this.props.taskName));

                const customFormObs =
                        services.getEvidenceEffectsRepository().findLatestFormSnapshotByServiceRecipientId(
                                this.props.serviceRecipientId,
                                EvidenceGroup.fromName(this.props.taskNameGroup || this.props.taskName));
                    collectionStateObservableFromSubscription(customFormObs) // TODO: This would be an ObjectSubscription if we
                            .then(it =>
                            it.subscribe({
                        error: exception => this.setState({error: exception}),
                        next: (formState) => {
                            for (let [_, value] of formState) {
                                this.setState({formEvidence: value.current.getDto()});
                            }
                        }
                                // complete: () => this.setState({loading: false /* perhaps..*/ })
                    })
            );


        workQ.then(formEvidence => {
                    let formDefinition: FormDefinition | null;
                    if (formEvidence && formEvidence.formDefinitionUuid) {
                        formDefinition = referral.features.findFormDefinition(formEvidence.formDefinitionUuid);
                    } else {
                        const formDefinitionUuid = referral.configResolver.getServiceType().getTaskDefinitionSetting(this.props.taskName,  "formDefinition");
                        formDefinition = formDefinitionUuid && referral.features.findFormDefinition(formDefinitionUuid);
                    }
                    // catch early if we are expecting something, here if we have a uuid we are expecting a parsed FormDefinition
                    this.setState({
                        formEvidence,
                        formDefinition,
                        serviceRecipientWithEntities: referral,
                        readOnlyFinal: readOnlyFinal
                    });
                });
                return workQ;
            });
    }

    /**
     * Indicate any changes to the form.
     */
    hasFormChanges(): boolean {
        if (!this.component) {
            return false; // no form to show
        }
        const cmd = this.createFormCommand();
        // includes form change, or notes
        return cmd.hasChanges();
    }

    getErrors(): string[] {
        if (!this.component) {
            return []; // no form to show
        }
        const errors: string[] = [];
        if (this.component.state.hasErrors) {
            errors.push('form missing required fields');
        }
        return errors;
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const cmd = this.createFormCommand();
        if (cmd) {
            commandQueue.addCommand(cmd);
        }
        if (this.state.noteCmdQueue.size() > 0) {
            commandQueue.addQueue(this.state.noteCmdQueue);
        }
    }

    createFormCommand() {
        if (!this.component) {
            return null; // no form to show
        }
        const patchIn = this.component.props.formEvidence?.form || {};
        const patchOut = JSON.stringify(this.component.state.outputFormData) == "{}" ? patchIn : this.component.state.outputFormData;
        const patch = diff(patchIn, patchOut);
        //console.log("patch: %o", patch);

        if (this.state.formDefinition) {
            const cmd = FormEvidenceUpdateCommand.create(this.props.serviceRecipientId, this.state.workUuidResolver.getWorkUuid(),
                    EccoDateTime.nowLocalTime(), EvidenceGroup.fromName(this.props.taskNameGroup || this.props.taskName),
                    this.props.taskName, this.props.taskHandle, patch, this.state.formDefinition.uuid);
            return cmd;
        }
        return null;
    }

    showHistory() {
        CustomFormHistoryListControl.showInModal(this.props.serviceRecipientId, this.props.taskName);
    }

    addComment() {
        const onSubmit = (form: EvidenceCommentForm) => {
            const cmdQ = new CommandQueue();
            form.emitChangesTo(cmdQ);
            this.setState({noteCmdQueue: cmdQ});
            return Promise.resolve();
        };

        const evidenceDef = EvidenceDef.fromTaskName(this.state.serviceRecipientWithEntities.features,
                this.state.serviceRecipientWithEntities.configResolver.getServiceType(), this.props.taskName);
        let form = new EvidenceCommentForm(this.state.serviceRecipientWithEntities, evidenceDef, () => onSubmit(form),
            () => this.state.workUuidResolver.getWorkUuid(), undefined, undefined, this.state.noteCmdQueue,
            undefined, "all");

        // change the buttons from close/save to reset/close - before calling 'showForm'
        const resetComment: ActionState = {
            label: "reset",
            clickedLabel: "reset",
            style: "link",
            onClick: () => {
                this.setState({noteCmdQueue: new CommandQueue()});
                form.callOnFinished();
                return Promise.resolve();
            },
            disabled: true
        };
        const actions = [resetComment, form.getSaveAction("add note", "add note")];
        form.setActions(actions);

        showFormInModalDom(form, false, undefined, false);
    }

    render() {
        return (
            <CustomSubformLayout
                formDefinition={this.state.formDefinition}
                formEvidence={this.state.formEvidence}
                readOnlyFinal={this.state.readOnlyFinal}
                renderer={this.props.renderer}
                clearCallback={this.props.clearCallback}
                printableLink={this.props.printableLink}
                historyLink={this.props.historyLink}
                serviceRecipientId={this.props.serviceRecipientId}
                taskName={this.props.taskName}
                taskHandle={this.props.taskHandle}
                noteQueueSize={this.state.noteCmdQueue.size()}
                serviceRecipientWithEntities={this.state.serviceRecipientWithEntities}
                onShowHistory={() => this.showHistory()}
                onAddComment={() => this.addComment()}
                onResetData={() => this.component.resetData()}
                onComponentRef={(c) => this.component = c!}
            />
        );
    }

}

export const GuidanceFormLoad: FC<{formDefinitionUuid: string | undefined}> = props => {
    const {sessionData} = useServicesContext();
    if (!props.formDefinitionUuid) {
        return null;
    }
    const guidanceFormDef = sessionData && sessionData.findFormDefinition(props.formDefinitionUuid);
    return guidanceFormDef && getGuidanceForm(guidanceFormDef);
}
export function getGuidanceForm(formDefinition: FormDefinition) {
    return <CustomSchema
              formEvidence = {null}
              formDefinition = {formDefinition}
              readOnly = {true}
    />;
}

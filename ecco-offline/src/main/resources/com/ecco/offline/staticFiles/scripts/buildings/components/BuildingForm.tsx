import * as React from "react"
import {BuildingEditor, CareRunEditor} from "ecco-components";
import {showInCommandForm} from "../../components/CommandForm";
import {ServicesContextProvider} from "../../offline/ServicesContextProvider";

// Pops up a building form to add/edit
// Called from:
//  - new/edit in BuildingsListControl (main welcome menu & newer page with UnitWrapper)
//        - refactored to handle the parent as the bldg currently on
//  - new unit in BuildingOverviewControl (unit, but older bldg page)
//  - new/edit run in RotaWorkerJobView
export function showBuildingEditor(srId: number | undefined, newParentBuildingId?: number | undefined) {
    showInCommandForm(
        <ServicesContextProvider>
            <BuildingEditor serviceRecipientId={srId} newParentBuildingId={newParentBuildingId} modal={true}/>
        </ServicesContextProvider>
    );
}
export function showCareRun(srId: number | undefined, newParentBuildingIds?: number[] | undefined) {
    showInCommandForm(
        <ServicesContextProvider>
            <CareRunEditor serviceRecipientId={srId} newParentBuildingIds={newParentBuildingIds}/>
        </ServicesContextProvider>
    );
}

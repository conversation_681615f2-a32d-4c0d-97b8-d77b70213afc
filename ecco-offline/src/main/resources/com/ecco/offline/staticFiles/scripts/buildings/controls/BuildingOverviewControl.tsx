import $ = require("jquery");
import _ = require("lodash");
import * as events from "../../common/events";

import BaseAsyncDataControl from "../../controls/BaseAsyncDataControl";
import DL from "../../controls/DefinitionList";
import TabbedContainer from "../../controls/TabbedContainer";
import ChecksEvidenceForm from "../../evidence/multi-instance/ChecksEvidenceForm";
import {SessionDataService} from "../../feature-config/SessionDataService";
import {applicationRootPath} from "application-properties";
import {apiClient, ServiceAgreementsView} from "ecco-components";
import {showReactInModal} from "ecco-components-core";
import {Building as BuildingDto, SessionData} from "ecco-dto";
import {BuildingAjaxRepository, EvidenceGroup, TaskNames} from "ecco-dto";
import {withAuthErrorHandler} from "ecco-offline-data";
import {WorkersListControl} from "../../hr/WorkersListControl";
import * as React from "react";
import {ReferralsListControl} from "../../referral/ReferralsListControl";
import {loadServiceRecipientAndAttach} from "../../workflow/tasklist/TasksControl";
import {componentAsElementForMui} from "../../components/MUIAsElement";
import {OverviewSummary} from "../../controls/layout/OverviewSummary";
import ChecklistEvidenceForm from "../../evidence/checklist/ChecklistEvidenceForm";
import GoalsControl from "../../evidence/tabular/GoalsControl";
import CommandHistoryListControl from "../../service-recipients/CommandHistoryListControl";
import {attachmentsEnhance} from "../../attachments/AttachmentsControl";
import {BuildingsListControl} from "ecco-components";
import {buildingOverviewPaneAsElement} from "../../rota/components/BuildingOverviewPane";
import {CalendarWithStaffOverlay} from "ecco-calendar";
import {ServicesContextProvider} from "../../offline/ServicesContextProvider";

const repository = new BuildingAjaxRepository(apiClient);

class BackingData {
    constructor(public building: BuildingDto,
            public features: SessionData) {
    }
}

export function serviceAgreementsAsElement(srId: number) {
    return componentAsElementForMui(
        <ServiceAgreementsView serviceRecipientId={srId}  />,
    "agreementsList");
}


function showChecklistForm(data: BackingData) {
    ChecklistEvidenceForm.showInModalByIds(data.building.serviceRecipientId, "checklist", "needsChecklist");
}

function showCalendar() {
    const CalendarElm = <ServicesContextProvider>
        <CalendarWithStaffOverlay/>
    </ServicesContextProvider>
    showReactInModal("calendar", CalendarElm,
        {
            action: "close",
            saveEnabled: false,
            maxWidth: "lg"
        });
}

export function rotaLinks($menu: $.JQuery, data: BackingData) {

    if (data.features.hasTopLevelGroup('rota', false)) {

        if (data.features.isEnabled("rota.services") && data.building.calendarId) {
            const svccats = data.features.getRestrictedServiceCategorisations()
                    .filter(sc => sc.buildingId == data.building.buildingId)
                    .map(sc => sc.id)
            if (svccats.length > 0) {
                const svccatCsv = svccats.join(",")
                $menu.append($("<a>")
                        .addClass("btn")
                        .attr("href", `${applicationRootPath}nav/w/welcome/rota/week/workers:all/svccats:${svccatCsv}`)
                        .text("rota"));
                if (data.features.isEnabled("rota.shifts")) {
                    $menu.append($("<a>")
                            .addClass("btn")
                            .attr("href", `${applicationRootPath}nav/w/welcome/rota/week/careruns:all/svccats:${svccatCsv}`)
                            .text("run builder"));
                }
            // } else {
            //     $menu.append($("<span>").text("rota: building has no associated services"));
            }
        }

        if (data.features.isEnabled("buildings.overview.tabs.agreements") && data.building.calendarId) {
            $menu.append($("<a>")
                    .addClass("btn")
                    .attr("href", `${applicationRootPath}nav/w/welcome/buildings/${data.building.buildingId}/rota/week`)
                    .text("rota"));
            if (data.features.isEnabled("rota.shifts")) {
                $menu.append($("<a>")
                        .addClass("btn")
                        .attr("href", `${applicationRootPath}nav/w/welcome/buildings/${data.building.buildingId}/runs/day`)
                        .text("run builder"));
            }
        }

    }

    return $menu;
}

function getMenuItems(data: BackingData) {
    const $menu = $("<div>").addClass("text-center");
    rotaLinks($menu, data);

//        $menu.append( $("<a>").addClass("btn").text("configure checks")
//            .click( () => {
//                EvidenceDelegatingForm.showInModalByIds(data.building.serviceRecipientId, "Configure Checks", "needsAssessment");
//            })
//        );

    $menu.append("<br>");
    if (data.features.isEnabled("buildings.overview.tabs.defects")) {
        $menu.append($("<a>").addClass("btn")
            .attr("href", `${applicationRootPath}nav/risk-history/bldg/${data.building.buildingId.toString()}`)
            .text("defects history"));
    }

    if (data.features.isEnabled("buildings.overview.menu.checklist")) {
        $menu.append($("<button>").addClass("btn btn-link")
            .click(() => showChecklistForm(data))
            .text("checklist"));
    }

    $menu.append($("<a>")
            .addClass("btn")
            .click(() => showCalendar())
            .text("calendar"));

    return $menu;
}

/**
 * Older building file
 */
class BuildingOverviewControl extends BaseAsyncDataControl<BackingData> {

    private tabs: TabbedContainer = new TabbedContainer();

    constructor(private buildingId: number, private sidebar: OverviewSummary) {
        super();
        this.element();
    }

    /** Trigger async load and render to the container */
    protected fetchViewData(): Promise<BackingData> {
        return withAuthErrorHandler(
            repository.findOneBuilding(this.buildingId).then( building =>
                SessionDataService.getFeatures().then( features =>
                    new BackingData(building, features)
                )
            )
        );
    }

    protected render(data: BackingData) {
        this.element()
            .empty()
            .append(this.tabs.element());
        this.renderBuilding(data.building);
        this.renderTasks(data.building);
        if (!data.building.parentId) {
            this.renderStaff(data.building);
            this.renderChildBuildings(data.building);
        }
        this.renderResidents(data.building);
        this.renderAttachments(data.building);
        if (data.features.isEnabled("buildings.overview.tabs.support")) {
            this.renderChecks(data.building);
        }
        this.renderForwardPlan(data.building);
        this.renderHistory(data.building);
        if (data.features.isEnabled("buildings.overview.tabs.agreements")) {
            this.renderAgreements(data.building);
        }
        if (data.features.isEnabled("buildings.overview.tabs.defects")) {
            this.renderDefects(data.building);
        }
        this.setMenu(data);
        this.sidebar.setTitle(data.building.name);
        const list1 = new DL();
        this.sidebar.getSection1().empty().append(list1.element());
        if (data.building.address) {
            const parts = data.building.address.address.filter((a) => !!a);
            list1.addEntryHtml("address",
                _.escape(parts.join(", ")) + "<br>" + _.escape(data.building.address.postcode) );
        }
        const list2 = new DL();
        this.sidebar.getSection2().empty().append(list2.element());
        list2.addNumEntry("bldg-id:", data.building.buildingId);
    }

    private renderBuilding(building: BuildingDto) {
        const overviewTab = $("<div>").addClass("container-fluid top-gap-15").css("padding-bottom", "50px");
        this.tabs.append("building", "building", overviewTab);
        overviewTab.append(buildingOverviewPaneAsElement(building.serviceRecipientId, building.buildingId))
    }

    private renderTasks(building: BuildingDto) {
        const tasksTab = $("<div>").addClass("container-fluid top-gap-15");
        this.tabs.append("tasks", "tasks", tasksTab);
        loadServiceRecipientAndAttach(tasksTab.get(0), building.serviceRecipientId);
    }

    private renderStaff(building: BuildingDto) {
        const peopleTab = $("<div>").addClass("container-fluid top-gap-15");
        this.tabs.append("staff", "staff", peopleTab);
        const control = new WorkersListControl(building.buildingId);
        peopleTab.append(control.element());
        control.load();
    }


    private renderChildBuildings(building: BuildingDto) {
        const $childTab = $("<div>").addClass("container-fluid top-gap-15");
        this.tabs.append("units", "units", $childTab);

        // Render the React BuildingsListControl component
        const buildingsListElement = componentAsElementForMui(
            <BuildingsListControl srId={building.serviceRecipientId} />,
            "buildingsList"
        );

        $childTab.append(buildingsListElement);
    }

    // render the residents - as per newer ResidentWrapper
    private renderResidents(building: BuildingDto) {
        const peopleTab = $("<div>").addClass("container-fluid top-gap-15");
        this.tabs.append("residents", "residents", peopleTab);
        const control = new ReferralsListControl(false, building.buildingId, true);
        peopleTab.append(control.element());
        control.load();
    }

    private renderAttachments(building: BuildingDto) {
        const $attachmentsTab = $("<div>").addClass("container-fluid top-gap-15 bottom-gap-15")
        this.tabs.append("attachments", "attachments", $attachmentsTab);
        attachmentsEnhance($attachmentsTab, building.serviceRecipientId);
    }

    private renderAgreements(building: BuildingDto) {
        this.tabs.append("agreements", "agreements",
            $("<div>")
                .addClass("container-fluid v-gap-15")
                .append(serviceAgreementsAsElement(building.serviceRecipientId)));
        <ServiceAgreementsView
            serviceRecipientId={building.serviceRecipientId}
        />
    }

    private renderChecks(building: BuildingDto) {
        const tab = $("<div>").addClass("container-fluid top-gap-15 bottom-gap-15");
        this.tabs.append("checks", "checks setup", tab);
        const $container = $("<div>").appendTo(tab);
        const $footer = $("<div>").addClass("col-xs-12");
        $("<div>").addClass("row").append($footer).appendTo(tab);
        const form = ChecksEvidenceForm.loadAndAttach(building.serviceRecipientId, $container, $footer);
    }

    private renderHistory(building: BuildingDto) {
        const $fwdTab = $("<div>").addClass("container-fluid top-gap-15 bottom-gap-15")
        this.tabs.append("checksHistory", "checks history", $fwdTab);
        // NB this is only for this checklist, so show the detail
        const history = CommandHistoryListControl.createWithIds(building.serviceRecipientId, EvidenceGroup.needs, null, true);
        history.load();
        $fwdTab.append(history.domElement());
    }

    private renderDefects(building: BuildingDto) {
        const tab = $("<div>").addClass("container-fluid top-gap-15 bottom-gap-15");
        this.tabs.append("defects", "defects", tab);
        tab.append("");
    }

    private renderForwardPlan(building: BuildingDto) {
        const $fwdTab = $("<div>").addClass("container-fluid top-gap-15 bottom-gap-15")
        this.tabs.append("forwardPlan", "checks due", $fwdTab);

        const $checklistLink = $("<div>").addClass("pull-right").appendTo($fwdTab);
        const checklistDue = () => {
            ChecklistEvidenceForm.showInModalByIds(building.serviceRecipientId, "checks due",
                TaskNames.needsReduction, undefined, true);
        }
        $checklistLink.append($("<a>").text("complete due").click(checklistDue));

        const $container = $("<div>").appendTo($fwdTab);
        GoalsControl.loadAndAttachForwardPlan($container, "forwardPlan", building.serviceRecipientId, false, true, false);
    }

    private setMenu(data: BackingData) {
        const $menu = getMenuItems(data);
        events.MenuUpdateEvent.bus.fire( new events.MenuUpdateEvent("nav", $menu) );
        return $menu;
    }
}
export default BuildingOverviewControl;
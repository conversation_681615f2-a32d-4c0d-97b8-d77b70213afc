import {Result, WebApiError} from "@eccosolutions/ecco-common";
import {
    Button,
    CircularProgress,
    Dialog,
    DialogContent,
    DialogTitle,
    Grid,
    Step,
    StepButton,
    StepContent,
    Stepper
} from "@eccosolutions/ecco-mui";

import {applicationRootPath} from "application-properties";
import {AsyncSessionData} from "ecco-components";
import {Notifications, DomElementContainer, ErrorBoundary} from "ecco-components-core";
import {SchemaForm, update, UpdateSpec} from "ecco-components";
import {
    ApiClient,
    CustomFormFields,
    FormDefinition,
    implementInterface,
    ObjectSchemaDto,
    ServiceDto,
    SessionData,
    SessionDataAjaxRepository,
    SessionDataDto,
    SessionDataGlobal,
    TaskNames
} from "ecco-dto";
import * as React from "react";
import {MUISchemaForm} from "../../components/MUISchemaForm";
import diff = require("json-patch-gen");
import {IncidentDetailsDto, IncidentFields} from "../../referral/components/ReferralDetailsForm";
import {ServicesContextProvider} from "../../offline/ServicesContextProvider";
import ServiceProjectSelectionControl from "../../entity-restrictions/ServiceProjectSelectionControl";
import {ifNeededSetDefaultGlobalEccoAPI} from "../../ifNeededSetDefaultGlobalEccoAPI";
import {CommandQueue, ServiceRecipientAssociatedContactCommand} from "ecco-commands";
import {showErrorAsAlert} from "ecco-offline-data";

const {add} = Notifications;


const apiClientInbound = new ApiClient(`${applicationRootPath}api/inbound/`,
    null, () => true);

const sessionDataRepository = new SessionDataAjaxRepository(apiClientInbound);

interface Props {
    serviceCategorisationId: number | null;
    linkContactId?: number | undefined;
    schema?: ObjectSchemaDto | undefined;
    schemaUri?: string | undefined;
}

type StepIndex = 0 | 1 | 2;
const StepIndexEnd = 2;

interface State {
    saved?: boolean | undefined;
    saved_id?: number | undefined;
    stepIndex?: StepIndex | undefined,
    logoPath: string,
    dto: object,
    incidentDetails: IncidentDetailsDto;
    formData: CustomFormFields,
    errors: Record<string, string|boolean>,
    schema: ObjectSchemaDto;
    globalConfig: SessionDataGlobal | null;
    serviceEmail: string | null;
    formDefinitionKey: FormDefinition;
    formDefinitionForm: FormDefinition;
    formDefinitionFull: FormDefinition;
    networkError: string;
}

const Entry = (props) => <Grid item sm={6} xs={12} >{props.children}</Grid>;

const incident_serviceTypeId = -500;

export class Wizard extends MUISchemaForm<Props, State> {

    private serviceProjectSelection: ServiceProjectSelectionControl = null;

    constructor(props: Props) {
        super(props);
        this.state = {
            saved: false,
            stepIndex: 0,
            logoPath: "",
            dto: {
                serviceCategorisationId: props.serviceCategorisationId,
                serviceTypeId: incident_serviceTypeId // part of the dto which maps to InboundIncidentResource
            },
            incidentDetails: {reportedById: null, reportedBy: null, reportedByContact: null, categoryId: null, emergencyServicesInvolved: null, hospitalisationInvolved: null},
            formData: {} as CustomFormFields,
            errors: {},
            schema: props.schema,
            serviceEmail: null,
            globalConfig: null,
            formDefinitionKey: null,
            formDefinitionForm: null,
            formDefinitionFull: null,
            networkError: null
        };
    }

    public override componentDidMount() {
        // NB repository has a client with: api/inbound/
        let globalConfigQ = sessionDataRepository.getGlobalConfigDto();
        let schemaQ = Promise.resolve(null);
        if (this.props.schemaUri && !this.props.schema) {
            schemaQ = this.loadSchema();
        }

        Promise.all([globalConfigQ, schemaQ])
            .then( ([globalConfig, schema] ) => {
            const logoId = globalConfig.settings["com.ecco:LOGO_FILE_ID"];
            const logoPath = logoId
                ? `${applicationRootPath}api/images/logo/${logoId}`
                : null;

            const sessionDataGlobal = new SessionDataGlobal(globalConfig);

            ifNeededSetDefaultGlobalEccoAPI(new SessionData(globalConfig as SessionDataDto));
            // NB other uses of service dropdown's use restrictedEntityRepository.findRestrictedServicesProjects
            // and so don't see the -ve services (unless given permission), eg ServicesProjects (in ReferralsListControl)
            // and which already filter out hideOnNew and hideOnList
            // (see NewReferralWizard with entityRestrictionsRepository or allowInboundServices)
            // so, since we are using a global list here (without restrictions), we need to do our own filtering here (in incidents)
            const services = sessionDataGlobal.getServiceCategorisationServices()
                    .filter(s => s.id >= 0)
                    .filter(s => !sessionDataGlobal.getServiceTypeById(s.serviceTypeId).isHideOnNew()
                            && !sessionDataGlobal.getServiceTypeById(s.serviceTypeId).isHideOnList()
                    );
            this.serviceProjectSelection = new ServiceProjectSelectionControl(services,
      {
                  serviceId: this.props.serviceCategorisationId && sessionDataGlobal.getServiceCategorisation(this.props.serviceCategorisationId).serviceId,
                  projectId: undefined
                },
       (selection) => {
                   this.setState(prev => {
                       const p = {...prev.dto}
                       p["serviceCategorisationId"] = sessionDataGlobal.getServiceCategorisationByIds(selection.serviceId, selection.projectId).id;
                       return {...prev, dto: p}
                   });
               }
            );
            this.serviceProjectSelection.element().find(".e-row").removeClass("e-row"); // Hack for now

            this.setState({
                  schema: schema || this.props.schema,
                  logoPath: logoPath,
                  globalConfig: sessionDataGlobal
            });
        });
    }

    // called on a change, not the initial render (so careful of default values needing data)
    public override componentDidUpdate(prevProps: Props, prevState: State, prevContext: any) {

        const serviceCategorisationId = Number(this.state.dto["serviceCategorisationId"]);

        // ignore if no service because we can't choose a definition without the service
        // error if no session data loaded because we need this - loaded on startup, so user would have to be very quick
        if (!this.state.globalConfig || !serviceCategorisationId) {
            // if (!this.sessionData) {
            //     // could instead trigger below code when sessionData is loaded
            //     throw new Error("SessionData should be loaded by now");
            // }
            return;
        }

        // change the form def if the category changes - and we have some config in it
        // the form gets assigned/saved to the file instantly, there is no real 'blank' config option
        const categoryId = Number(this.state.incidentDetails.categoryId);
        const formDefCategory = this.state.globalConfig.getListDefinitionEntryById(categoryId)?.getFormDefUuid();
        if (formDefCategory && prevState.incidentDetails.categoryId != categoryId) {
            const serviceId = this.state.globalConfig.getServiceCategorisation(serviceCategorisationId).serviceId;
            const service = this.state.globalConfig.getService(serviceId);
            this.updateFormDef(formDefCategory, service);
        }

        // change the form def if the service changes - and we have some config in it
        if (!formDefCategory && prevState.dto["serviceCategorisationId"] != serviceCategorisationId) {

            // ** getting the ServiceType domain is not that obvious **
            // 1) get the serviceTypeId, and then the dto (once getSessionData has been called)
            //    and then get the domain from a constructor
            //  const serviceTypeId = this.sessionData.getService(serviceId).serviceTypeId;
            //  const serviceTypeDto = ServiceType.getServiceType(serviceTypeId);
            //  const serviceTypeDomain = new ServiceType(serviceTypeDto);

            // 2) get the dto directly from the sessionData, then the domain
            //  const sessionDataDto = this.sessionData.getDto().serviceTypesById[serviceTypeId];
            //  const serviceTypeDomain = new ServiceType(serviceTypeDto);

            // 3) get the domain directly from an ajax call (which is cached)
            //    which does not require session data, BUT we do need the serviceTypeId
            //    and if we get that, we can't use the resulting getServiceType() since it won't be populated!

            const serviceId = this.state.globalConfig.getServiceCategorisation(serviceCategorisationId).serviceId;
            const service = this.state.globalConfig.getService(serviceId);
            // FIXED CONFIG
            const serviceTypeDomain = this.state.globalConfig.getServiceTypeById(incident_serviceTypeId);

            // only call setState once in componentDidUpdate

            // split the array of form defs - to specifically show them in different places
            const formDefinitionUuid = serviceTypeDomain.getTaskDefinitionSetting(TaskNames.referralDetails, "formDefinition");
            this.updateFormDef(formDefinitionUuid, service);
        }
    }

    private updateFormDef(formDefinitionUuid: string, service: ServiceDto) {
        if (!formDefinitionUuid) {
            return;
        }
        const formDefKey = this.state.globalConfig.findFormDefinition(formDefinitionUuid, 0);
        const formDefForm = this.state.globalConfig.findFormDefinition(formDefinitionUuid, 1);
        // ensure we have one to use
        const formDefFull = this.state.globalConfig.findFormDefinition(formDefinitionUuid, 2) ||
                this.state.globalConfig.findFormDefinition(formDefinitionUuid);
        if (formDefFull) {
            // trigger a state change to render the form
            // only call setState once in componentDidUpdate
            const updateSpec: UpdateSpec<State> = {};
            //updateSpec.stepIndex = {$set: 1};
            updateSpec.formDefinitionKey = {$set: formDefKey};
            updateSpec.formDefinitionForm = {$set: formDefForm};
            updateSpec.formDefinitionFull = {$set: formDefFull};
            updateSpec.serviceEmail = {$set: service.parameters && service.parameters["email.notification"]};
            this.setState(prevState => update(prevState, updateSpec));
        } else {
            this.setState({
              serviceEmail: service.parameters && service.parameters["email.notification"]
          });
        }
    }

    private loadSchema() {
        return apiClientInbound.get<ObjectSchemaDto>(this.props.schemaUri);
    }


    private handleNext = () => {
        const {stepIndex} = this.state;
        if (stepIndex < StepIndexEnd) {
            this.setState({stepIndex: stepIndex + 1 as StepIndex});
        }
        else {
            const errorMsgs: string[] = [];
            let someRequired = false;

            // this.state.errors.keys().map not valid, possibly also Object.values(this.state.agencyCategoryIndex).map
            for (const key in this.state.errors) {
                const v: boolean | string = this.state.errors[key];
                if (typeof v == "string") {
                    // some, such as postCode and serviceId set a 'required' string
                    if (v == "required") {
                        someRequired = true;
                    } else {
                        errorMsgs.push(`${key} ${v}`);
                    }
                } else {
                    if (v) {
                        someRequired = v;
                    }
                }
            }

            if (someRequired || errorMsgs.length > 0) {
                let msg = "please fix ";
                if (someRequired) {
                    msg = msg.concat("the required fields");
                }
                if (errorMsgs.length > 0) {
                    if (someRequired) {
                        msg = msg.concat(" and ");
                    }
                    msg = msg.concat("the following: " + errorMsgs.join(", "));
                }
                Notifications.add("refer-errors", msg);
            } else {
                this.save();
            }
        }
    };

    private handlePrev = () => {
        const {stepIndex} = this.state;
        if (stepIndex > 0) {
            this.setState({stepIndex: stepIndex - 1 as StepIndex});
        }
    };

    private save() {

        // this.props.schema.links.
        const uri = this.props.schemaUri.split("/$schema/")[0];
        add("save", "saving...");
        const patch = diff({}, this.state.formData);
        const serviceId = Number(this.state.dto["serviceId"]);
        const formDefinitionUuid = this.state.formDefinitionFull?.uuid;

        const dto = {...this.state.dto,
            reportedById: this.state.incidentDetails.reportedById,
            reportedBy: this.state.incidentDetails.reportedBy,
            reportedByContact: this.state.incidentDetails.reportedByContact,
            categoryId: this.state.incidentDetails.categoryId,
            emergencyServicesInvolved: this.state.incidentDetails.emergencyServicesInvolved,
            hospitalisationInvolved: this.state.incidentDetails.hospitalisationInvolved};

        const linkToContactIdQ = (srId: number, linkContactId: number | null) => {
            if (!this.props.linkContactId) {
                return Promise.resolve();
            }
            const cmd = new ServiceRecipientAssociatedContactCommand("add", srId, linkContactId);
            //cmd.changeAddedAssociatedTypeIds(null, this.state.listId.toString());
            const commandQueue = new CommandQueue();
            commandQueue.addCommand(cmd);
            return commandQueue
                .flushCommands(false)
                .catch(e => showErrorAsAlert(e));
        }
        apiClientInbound.post<Result>(uri, {dto: dto, formDefinitionUuid: formDefinitionUuid, formData: patch})
            .then( result => {
                // message is fudged with the srId of the incident
                return linkToContactIdQ(parseInt(result.message), this.props.linkContactId).then(() => {
                    this.setState({
                          saved: true,
                          saved_id: parseInt(result.id)
                      });
                    // InboundIncident returns from IncidentController#createImport the Result(incidentId)
                    add("save", "Saved");
                })
            })
            .catch( (error: WebApiError) => {
                this.setState({networkError: error.toString()});
            });
    }

    private renderStepActions(step) {
        return (
            <div style={{margin: '12px 0'}}>
                {step > 0 && (
                    <Button
                        variant="text"
                        disableTouchRipple={true}
                        disableFocusRipple={true}
                        onClick={this.handlePrev}
                    >
                        back
                    </Button>
                )}
                <Button
                    variant="contained"
                    disableTouchRipple={true}
                    disableFocusRipple={true}
                    color="primary"
                    //disabled={step == 3 && this.hasErrors()}
                    onClick={this.handleNext}
                    style={{marginRight: 12}}
                >
                    {step < StepIndexEnd ? "Next" : "Done"}
                </Button>
            </div>
        );
    }

    private getStepContent(step: StepIndex) {
        const dtoStateSetter = dto => this.setState({dto});
        const schema = implementInterface(this.state.schema).asObjectSchema();
        const serviceId = Number(this.state.dto["serviceId"]);

        // mimic the MUISchemaForm error logic for our bootstrap fields
        // so that the submit button remains disabled if invalid

        // we don't need to set an initial error state for 'formData' because liveValidation triggers the initial state (by observation)
        //this.state.errors['formData'] = // somehow force validation check

        switch (step) {
            case 0:
                //return this.selectField(schema.properties, "serviceId", this.props.serviceId != null);
                return this.serviceProjectSelection && <DomElementContainer content={this.serviceProjectSelection.element()[0]}/>

            case 1:
                return (<div>
                    {/* alternative approach to pushing 'required' into ComponentUtils
                    <FormGroup validationState={this.state.dto['firstName'] == null ? "error" : "success"}>
                        {textInput("firstName", "First name", dtoStateSetter, this.state.dto)}
                    </FormGroup>
                    */}

                    <Grid container>
                        <ErrorBoundary>
                            <AsyncSessionData promiseFn={() => Promise.resolve(new SessionData(this.state.globalConfig.getDto() as SessionDataDto))}>
                                <ServicesContextProvider>
                                    <IncidentFields dto={this.state.incidentDetails} setter={dto => this.setState({incidentDetails: {...this.state.incidentDetails, ...dto}})}/>
                                </ServicesContextProvider>
                            </AsyncSessionData>
                        </ErrorBoundary>
                    </Grid>

                    {this.state.formDefinitionKey && <SchemaForm
                            readOnly={false}
                            formData={this.state.formData}
                            formDefinition={this.state.formDefinitionKey}
                            onChange={(data, hasErrors) => {this.handleChangeForm(data, hasErrors)}}
                            resetData={false}
                    />}

                </div>);

            case 2:
                return <div>
                    <SchemaForm
                        readOnly={false}
                        formData={this.state.formData}
                        formDefinition={this.state.formDefinitionForm || this.state.formDefinitionFull}
                        onChange={(data, hasErrors) => {this.handleChangeForm(data, hasErrors)}}
                        resetData={false}
                    />
                </div>;
        }
    }

    private handleChangeForm(data: any, hasError: boolean) {
        this.setState(prevState => {
            const errorsUpdated = prevState.errors;
            errorsUpdated['formData'] = hasError;
            return update(prevState, {
                    formData: {$set: data},
                    errors: {$set: errorsUpdated}
                });
        });
    }

    override render() {
        return (this.state.schema
                ? <div style={{maxWidth: 760, maxHeight: 400, margin: 'auto'}}>
                    {this.state.networkError && <Dialog open={true}>
                        <DialogTitle>Something went wrong</DialogTitle>
                        <DialogContent>
                            There was an error: {this.state.networkError}
                            <p>Please 'dismiss' and try again.</p>
                            <Button
                                style={{float: "right"}}
                                onClick={() => this.setState({networkError: null})}
                            >
                                Dismiss
                            </Button>
                        </DialogContent>
                    </Dialog>}
                    {this.state.saved
                        ? <div style={{padding: "24px"}}>
                            <h2 className="md-headline">Incident saved</h2>
                            <p>Your incident has been saved and will be processed soon.</p>
                            <p>Your reference is: <b>i-id {this.state.saved_id}</b></p>
                            {this.state.serviceEmail &&
                                <p>Please send any additional information to: <a href={`mailto:${this.state.serviceEmail}?subject=ECCO New Incident (info on i-id ${this.state.saved_id})`}>{this.state.serviceEmail}</a></p>
                            }
                            <Button
                                component={"a"}
                                // linkButton={true}
                                style={{float: "right"}}
                                disableTouchRipple={true}
                                disableFocusRipple={true}
                                onClick={() => window.location.reload()}
                            >
                                Add another
                            </Button>
                        </div>
                        : <div style={{padding: "24px"}}>
                            {this.state.logoPath ? <img style={{maxHeight: '75px'}} src={this.state.logoPath}/> : null}
                            <h3>Report an incident</h3>
                            <p>Incident details will be recorded in ECCO</p>
                            <Stepper
                                activeStep={this.state.stepIndex}
                                nonLinear={true}
                                orientation="vertical"
                            >
                                <Step>
                                    <StepButton style={{fontSize: "50px"}} onClick={() => this.setState({stepIndex: 0})}>
                                        Select the service that the incident relates to
                                    </StepButton>
                                    <StepContent>
                                        {this.getStepContent(0)}
                                        {this.renderStepActions(0)}
                                    </StepContent>
                                </Step>
                                <Step>
                                    <StepButton onClick={() => this.setState({stepIndex: 1})}>
                                        Enter key incident details
                                    </StepButton>
                                    <StepContent>
                                        {this.getStepContent(1)}
                                        {this.renderStepActions(1)}
                                    </StepContent>
                                </Step>
                                <Step>
                                    <StepButton onClick={() => this.setState({stepIndex: 2})}>
                                        Enter incident form
                                    </StepButton>
                                    <StepContent>
                                        {this.getStepContent(2)}
                                        {this.renderStepActions(2)}
                                    </StepContent>
                                </Step>
                            </Stepper>
                        </div>}
            </div>
            : <div key="progress" className="vertical-center">
                <div style={{width: '100%'}}>
                    <CircularProgress style={{margin: 'auto', display: 'block'}}/>
                </div>
            </div>
        );
    }
}
